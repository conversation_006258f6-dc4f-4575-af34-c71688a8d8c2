# PostgreSQL Database Connection
DB_URL=postgresql://evoque_user:<EMAIL>/evoque

# Legacy PostgreSQL Configuration
DB_HOST=dpg-d02ttkidbo4c73c3j560-a.oregon-postgres.render.com
DB_PORT=5432
DB_NAME=evoque
DB_USER=evoque_user
DB_PASSWORD=DU9TjUPoW9397L8rZq9RqfvTw5WVT7XW

# AI API Configuration
# OpenAI API for embeddings, OpenRouter API for chat

# API Keys
OPENAI_API_KEY=sk-proj-your-openai-api-key-here
OPENROUTER_API_KEY=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
PERPLEXITY_API_KEY=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

# Model Configuration
OPENROUTER_MODEL=google/gemini-2.5-flash-lite-preview-06-17
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Google Calendar API (for booking integration)
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI=http://localhost:4000/auth/google/callback

# JWT Configuration
JWT_SECRET=evoque-digital-secret-key-change-in-production
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# Server Configuration
PORT=4000
NODE_ENV=development
LOG_LEVEL=info

# CORS Configuration
CORS_ORIGIN=*

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# File Upload
MAX_FILE_SIZE=10485760

# Docker Compose PgAdmin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin