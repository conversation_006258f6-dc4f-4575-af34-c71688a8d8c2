import { gql } from 'graphql-tag';
import { userTypeDefs } from './user.schema';
import { venueTypeDefs } from './venue.schema';
import { knowledgeBaseTypeDefs } from './knowledgeBase.schema';
import { inquiryTypeDefs } from './inquiry.schema';
import { leadTypeDefs } from './lead.schema';
import { mediaTypeDefs } from './media.schema';
import { analyticsTypeDefs } from './analytics.schema';
import { authTypeDefs } from './auth.schema';
import { aiTypeDefs } from './ai.schema';
import { paymentTypeDefs } from './payment.schema';
import { adminTypeDefs } from './admin.schema';
import { calendarTypeDefs } from './calendar.schema';

// Base schema with common types and directives
const baseTypeDefs = gql`
  # Scalar types
  scalar DateTime
  scalar Date
  scalar JSON
  
  # Pagination input
  input PaginationInput {
    page: Int
    limit: Int
    cursor: String
  }
  
  # Pagination info
  type PageInfo {
    hasNextPage: Boolean!
    hasPreviousPage: Boolean!
    startCursor: String
    endCursor: String
    totalCount: Int
  }
  
  # Directives
  directive @auth on FIELD_DEFINITION
  directive @hasRole(role: String!) on FIELD_DEFINITION
  directive @hasPermission(permission: String!) on FIELD_DEFINITION
  
  # Root types
  type Query {
    _: Boolean
  }
  
  type Mutation {
    _: Boolean
  }
  
  type Subscription {
    _: Boolean
  }
`;

// Combine all type definitions
export const typeDefs = [
  baseTypeDefs,
  userTypeDefs,
  venueTypeDefs,
  knowledgeBaseTypeDefs,
  inquiryTypeDefs,
  leadTypeDefs,
  mediaTypeDefs,
  analyticsTypeDefs,
  authTypeDefs,
  aiTypeDefs,
  paymentTypeDefs,
  adminTypeDefs,
  calendarTypeDefs,
];