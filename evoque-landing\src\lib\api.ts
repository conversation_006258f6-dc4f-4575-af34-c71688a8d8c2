// API endpoint for the backend
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';

// API client helper
const apiClient = {
  async request(endpoint: string, options: RequestInit = {}) {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }
    
    return response.json();
  }
};

// Helper functions for database operations via API
export const dbHelpers = {
  // Save a new lead
  async saveLead(lead: Lead): Promise<{ data: Lead | null; error: any }> {
    try {
      const data = await apiClient.request('/api/leads', {
        method: 'POST',
        body: JSON.stringify({
          venueId: lead.venue_id,
          contactInfo: lead.contact_info,
          eventDetails: lead.event_details,
          score: lead.score,
          scoreFactors: lead.score_factors,
          status: lead.status,
          source: lead.source,
          insights: lead.insights
        })
      });
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Save a conversation
  async saveConversation(conversation: Conversation): Promise<{ data: Conversation | null; error: any }> {
    try {
      const data = await apiClient.request('/api/conversations', {
        method: 'POST',
        body: JSON.stringify({
          leadId: conversation.lead_id,
          messages: conversation.messages,
          aiContext: conversation.ai_context,
          humanTakeover: conversation.human_takeover
        })
      });
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Update conversation messages
  async updateConversationMessages(conversationId: string, messages: Message[]): Promise<{ error: any }> {
    try {
      await apiClient.request(`/api/conversations/${conversationId}`, {
        method: 'PUT',
        body: JSON.stringify({ messages })
      });
      return { error: null };
    } catch (error) {
      return { error };
    }
  },

  // Get venue profiles
  async getVenues(): Promise<{ data: Venue[] | null; error: any }> {
    try {
      const data = await apiClient.request('/api/venues');
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Get a specific venue by slug
  async getVenueBySlug(slug: string): Promise<{ data: Venue | null; error: any }> {
    try {
      const data = await apiClient.request(`/api/venues/slug/${slug}`);
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },
};
// Types for our database tables
export interface Lead {
  id?: string;
  venue_id?: string;
  contact_info: {
    name: string;
    email: string;
    phone?: string;
  };
  event_details?: {
    date?: string;
    guestCount?: number;
    budget?: string;
  };
  score?: number;
  score_factors?: any;
  status?: 'NEW' | 'HOT' | 'WARM' | 'COLD' | 'QUALIFIED' | 'CONVERTED';
  source?: string;
  insights?: string[];
  created_at?: string;
  updated_at?: string;
}

export interface Venue {
  id?: string;
  name: string;
  slug: string;
  description?: string;
  capacity?: {
    min: number;
    max: number;
    ideal_min?: number;
    ideal_max?: number;
  };
  pricing?: {
    starting: number;
    average: number;
    maximum: number;
    minimum?: number;
  };
  amenities?: string[];
  address?: any;
  settings?: any;
  created_at?: string;
  updated_at?: string;
}

export interface Conversation {
  id?: string;
  lead_id: string;
  messages: any[];
  ai_context?: any;
  human_takeover?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}