import React, { useState, useEffect } from 'react';
import { useMutation, useQuery, gql } from '@apollo/client';
import {
  CalendarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClipboardDocumentIcon,
  ArrowTopRightOnSquareIcon,
  Cog6ToothIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/context/AuthContext';

// GraphQL queries and mutations
const CHECK_CALENDAR_STATUS = gql`
  query CheckCalendarStatus($venueId: ID!) {
    calendarStatus(venueId: $venueId) {
      connected
      lastSync
      email
    }
  }
`;

const GENERATE_OAUTH_URL = gql`
  mutation GenerateOAuthUrl($venueId: ID!) {
    generateCalendarOAuthUrl(venueId: $venueId) {
      url
    }
  }
`;

const DISCONNECT_CALENDAR = gql`
  mutation DisconnectCalendar($venueId: ID!) {
    disconnectCalendar(venueId: $venueId)
  }
`;

interface CalendarStatus {
  connected: boolean;
  lastSync?: string;
  email?: string;
}

interface GoogleCalendarIntegrationProps {
  venueId: string;
}

const GoogleCalendarIntegration: React.FC<GoogleCalendarIntegrationProps> = ({ venueId }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showSetupGuide, setShowSetupGuide] = useState(false);
  const [credentials, setCredentials] = useState({
    clientId: '',
    clientSecret: '',
    redirectUri: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isConnecting, setIsConnecting] = useState(false);

  const { data: calendarData, loading, refetch } = useQuery(CHECK_CALENDAR_STATUS, {
    variables: { venueId },
    pollInterval: 5000, // Poll every 5 seconds when connecting
  });

  const [generateOAuthUrl] = useMutation(GENERATE_OAUTH_URL);
  const [disconnectCalendar] = useMutation(DISCONNECT_CALENDAR);

  const calendarStatus: CalendarStatus = calendarData?.calendarStatus || { connected: false };

  const setupSteps = [
    {
      title: 'Google Cloud Console Setup',
      description: 'Create and configure your Google Cloud project',
      completed: false
    },
    {
      title: 'Enable Calendar API',
      description: 'Enable the Google Calendar API for your project',
      completed: false
    },
    {
      title: 'Create OAuth Credentials',
      description: 'Set up OAuth 2.0 credentials for secure access',
      completed: false
    },
    {
      title: 'Connect Your Calendar',
      description: 'Authorize access to your Google Calendar',
      completed: calendarStatus.connected
    }
  ];

  const handleConnectCalendar = async () => {
    try {
      setIsConnecting(true);
      setErrors({});
      
      const { data } = await generateOAuthUrl({
        variables: { venueId }
      });
      
      if (data?.generateCalendarOAuthUrl?.url) {
        // Open OAuth URL in new window
        const authWindow = window.open(
          data.generateCalendarOAuthUrl.url,
          'google-auth',
          'width=500,height=600,scrollbars=yes,resizable=yes'
        );
        
        // Poll for window closure (indicates auth completion)
        const pollTimer = setInterval(() => {
          if (authWindow?.closed) {
            clearInterval(pollTimer);
            setIsConnecting(false);
            refetch(); // Refresh calendar status
          }
        }, 1000);
      }
    } catch (error: any) {
      setErrors({ connection: error.message || 'Failed to connect to Google Calendar' });
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnectCalendar({ variables: { venueId } });
      refetch();
    } catch (error: any) {
      setErrors({ disconnect: error.message || 'Failed to disconnect calendar' });
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const openGoogleCloudConsole = () => {
    window.open('https://console.cloud.google.com/', '_blank');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-5 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CalendarIcon className="h-6 w-6 text-primary-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">Google Calendar Integration</h3>
              <p className="text-sm text-gray-500">
                Connect your Google Calendar for real-time availability and automated booking management
              </p>
            </div>
          </div>
          {calendarStatus.connected && (
            <div className="flex items-center text-green-600">
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              <span className="text-sm font-medium">Connected</span>
            </div>
          )}
        </div>
      </div>

      <div className="px-6 py-5">
        {calendarStatus.connected ? (
          // Connected State
          <div className="space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start">
                <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-green-800">Calendar Connected Successfully</h4>
                  <div className="mt-2 text-sm text-green-700">
                    <p>Connected to: <span className="font-medium">{calendarStatus.email}</span></p>
                    {calendarStatus.lastSync && (
                      <p>Last sync: {new Date(calendarStatus.lastSync).toLocaleString()}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 mb-2">Features Enabled</h5>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>✅ Real-time availability checking</li>
                  <li>✅ Automated booking notifications</li>
                  <li>✅ Event creation and management</li>
                  <li>✅ Calendar synchronization</li>
                </ul>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 mb-2">Management</h5>
                <div className="space-y-2">
                  <button
                    onClick={() => refetch()}
                    className="w-full text-left text-sm text-primary-600 hover:text-primary-700"
                  >
                    🔄 Refresh Connection Status
                  </button>
                  <button
                    onClick={handleDisconnect}
                    className="w-full text-left text-sm text-red-600 hover:text-red-700"
                  >
                    🔌 Disconnect Calendar
                  </button>
                </div>
              </div>
            </div>

            {errors.disconnect && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mt-0.5 mr-3" />
                  <p className="text-sm text-red-700">{errors.disconnect}</p>
                </div>
              </div>
            )}
          </div>
        ) : (
          // Not Connected State
          <div className="space-y-6">
            {!showSetupGuide ? (
              // Quick Connect Option
              <div className="text-center py-8">
                <CalendarIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">
                  Connect Your Google Calendar
                </h4>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Enable real-time availability checking and automated booking management by connecting your Google Calendar.
                </p>
                
                <div className="space-y-3">
                  <button
                    onClick={handleConnectCalendar}
                    disabled={isConnecting}
                    className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isConnecting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                        Connecting...
                      </>
                    ) : (
                      <>
                        <CalendarIcon className="h-5 w-5 mr-2" />
                        Connect Google Calendar
                      </>
                    )}
                  </button>
                  
                  <div>
                    <button
                      onClick={() => setShowSetupGuide(true)}
                      className="text-sm text-primary-600 hover:text-primary-700"
                    >
                      Need help? View setup guide
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              // Setup Guide
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-medium text-gray-900">Setup Guide</h4>
                  <button
                    onClick={() => setShowSetupGuide(false)}
                    className="text-sm text-gray-500 hover:text-gray-700"
                  >
                    ← Back to quick connect
                  </button>
                </div>

                {/* Setup Steps */}
                <div className="space-y-4">
                  {setupSteps.map((step, index) => (
                    <div
                      key={index}
                      className={`border rounded-lg p-4 ${
                        step.completed
                          ? 'border-green-200 bg-green-50'
                          : currentStep === index + 1
                          ? 'border-primary-200 bg-primary-50'
                          : 'border-gray-200 bg-gray-50'
                      }`}
                    >
                      <div className="flex items-start">
                        <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                          step.completed
                            ? 'bg-green-600 text-white'
                            : currentStep === index + 1
                            ? 'bg-primary-600 text-white'
                            : 'bg-gray-400 text-white'
                        }`}>
                          {step.completed ? '✓' : index + 1}
                        </div>
                        <div className="ml-3 flex-1">
                          <h5 className="font-medium text-gray-900">{step.title}</h5>
                          <p className="text-sm text-gray-600 mt-1">{step.description}</p>
                          
                          {/* Step-specific content */}
                          {currentStep === index + 1 && !step.completed && (
                            <div className="mt-3">
                              {index === 0 && (
                                <div className="space-y-3">
                                  <p className="text-sm text-gray-700">
                                    1. Go to the Google Cloud Console
                                  </p>
                                  <button
                                    onClick={openGoogleCloudConsole}
                                    className="inline-flex items-center text-sm text-primary-600 hover:text-primary-700"
                                  >
                                    <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-1" />
                                    Open Google Cloud Console
                                  </button>
                                  <p className="text-sm text-gray-700">
                                    2. Create a new project or select an existing one
                                  </p>
                                  <button
                                    onClick={() => setCurrentStep(2)}
                                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200"
                                  >
                                    Next Step
                                  </button>
                                </div>
                              )}
                              
                              {index === 1 && (
                                <div className="space-y-3">
                                  <p className="text-sm text-gray-700">
                                    1. In the Google Cloud Console, go to "APIs & Services" → "Library"
                                  </p>
                                  <p className="text-sm text-gray-700">
                                    2. Search for "Google Calendar API" and click "Enable"
                                  </p>
                                  <button
                                    onClick={() => setCurrentStep(3)}
                                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200"
                                  >
                                    Next Step
                                  </button>
                                </div>
                              )}
                              
                              {index === 2 && (
                                <div className="space-y-3">
                                  <p className="text-sm text-gray-700">
                                    1. Go to "APIs & Services" → "Credentials"
                                  </p>
                                  <p className="text-sm text-gray-700">
                                    2. Click "Create Credentials" → "OAuth 2.0 Client IDs"
                                  </p>
                                  <p className="text-sm text-gray-700">
                                    3. Set application type to "Web application"
                                  </p>
                                  <div className="bg-gray-100 p-3 rounded border">
                                    <p className="text-xs text-gray-600 mb-2">Add this redirect URI:</p>
                                    <div className="flex items-center space-x-2">
                                      <code className="text-xs bg-white px-2 py-1 rounded border flex-1">
                                        {window.location.origin}/auth/google/callback
                                      </code>
                                      <button
                                        onClick={() => copyToClipboard(`${window.location.origin}/auth/google/callback`)}
                                        className="p-1 text-gray-500 hover:text-gray-700"
                                      >
                                        <ClipboardDocumentIcon className="h-4 w-4" />
                                      </button>
                                    </div>
                                  </div>
                                  <button
                                    onClick={() => setCurrentStep(4)}
                                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200"
                                  >
                                    Next Step
                                  </button>
                                </div>
                              )}
                              
                              {index === 3 && (
                                <div className="space-y-3">
                                  <p className="text-sm text-gray-700">
                                    Now connect your Google Calendar using the button below:
                                  </p>
                                  <button
                                    onClick={handleConnectCalendar}
                                    disabled={isConnecting}
                                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                                  >
                                    {isConnecting ? (
                                      <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                                        Connecting...
                                      </>
                                    ) : (
                                      <>
                                        <CalendarIcon className="h-4 w-4 mr-2" />
                                        Connect Google Calendar
                                      </>
                                    )}
                                  </button>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {errors.connection && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mt-0.5 mr-3" />
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-red-800">Connection Failed</h4>
                    <p className="text-sm text-red-700 mt-1">{errors.connection}</p>
                    <div className="mt-3">
                      <button
                        onClick={() => setShowSetupGuide(true)}
                        className="text-sm text-red-600 hover:text-red-700 underline"
                      >
                        View setup guide for troubleshooting
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default GoogleCalendarIntegration;