import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

/**
 * Create a complete demo venue with sample data
 */
async function createDemoVenue() {
  console.log('🏰 Creating demo venue...');

  try {
    // Create demo user
    const demoUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        firstName: 'Demo',
        lastName: 'User',
        phone: '******-0123',
        status: 'active',
        emailVerified: true,
        passwordHash: await bcrypt.hash('demo123', 10)
      }
    });

    console.log('✅ Demo user created:', demoUser.email);

    // Create demo venue
    const demoVenue = await prisma.venue.upsert({
      where: { slug: 'demo-venue' },
      update: {},
      create: {
        name: 'Enchanted Gardens Demo Venue',
        slug: 'demo-venue',
        description: 'A beautiful demonstration venue showcasing the Evoque platform capabilities. This venue features elegant gardens, stunning architecture, and world-class amenities perfect for weddings and special events.',
        address: '123 Demo Street',
        city: 'Demo City',
        state: 'CA',
        zipCode: '90210',
        country: 'USA',
        phone: '******-DEMO',
        email: '<EMAIL>',
        website: 'https://demo-venue.com',
        capacity: 200,
        priceRange: '$5,000 - $15,000',
        venueType: 'garden',
        status: 'active',
        featuredImage: 'https://images.unsplash.com/photo-1519167758481-83f29c8e8d4b',
        galleryImages: [
          'https://images.unsplash.com/photo-1519167758481-83f29c8e8d4b',
          'https://images.unsplash.com/photo-1464366400600-7168b8af9bc3',
          'https://images.unsplash.com/photo-1470229722913-7c0e2dbbafd3'
        ],
        amenities: [
          'Outdoor ceremony space',
          'Indoor reception hall',
          'Bridal suite',
          'Catering kitchen',
          'Parking for 100 cars',
          'Garden photography areas'
        ],
        policies: {
          cancellation: '30 days notice required',
          deposit: '50% deposit required to secure date',
          finalPayment: 'Final payment due 14 days before event'
        }
      }
    });

    console.log('✅ Demo venue created:', demoVenue.name);

    // Create venue user association
    await prisma.venueUser.upsert({
      where: {
        venueId_userId: {
          venueId: demoVenue.id,
          userId: demoUser.id
        }
      },
      update: {},
      create: {
        venueId: demoVenue.id,
        userId: demoUser.id,
        title: 'Owner',
        department: 'Management',
        isPrimary: true
      }
    });

    // Get demo subscription plan
    const demoPlan = await prisma.subscriptionPlan.findFirst({
      where: { name: 'Demo' }
    });

    if (demoPlan) {
      // Create demo subscription
      await prisma.subscription.upsert({
        where: { venueId: demoVenue.id },
        update: {},
        create: {
          venueId: demoVenue.id,
          planId: demoPlan.id,
          status: 'active',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
          metadata: {
            isDemoAccount: true,
            createdBy: 'setup-script'
          }
        }
      });

      console.log('✅ Demo subscription created');
    }

    // Create wedding packages
    const packages = [
      {
        name: 'Intimate Garden Wedding',
        description: 'Perfect for smaller celebrations with up to 50 guests. Includes ceremony in our rose garden and reception in the conservatory.',
        basePrice: 5000,
        maxGuests: 50,
        hoursIncluded: 6,
        details: {
          includes: [
            'Ceremony setup in rose garden',
            'Reception in conservatory',
            'Basic lighting and sound',
            'Tables and chairs for 50',
            'Bridal suite access'
          ]
        }
      },
      {
        name: 'Classic Garden Celebration',
        description: 'Our most popular package for medium-sized weddings up to 100 guests. Features both indoor and outdoor spaces.',
        basePrice: 8500,
        maxGuests: 100,
        hoursIncluded: 8,
        details: {
          includes: [
            'Ceremony in main garden',
            'Reception in grand hall',
            'Professional lighting and sound',
            'Tables, chairs, and linens',
            'Bridal and groom suites',
            'Coordinator for 8 hours'
          ]
        }
      },
      {
        name: 'Grand Estate Wedding',
        description: 'The ultimate wedding experience for up to 200 guests with access to our entire estate and premium amenities.',
        basePrice: 15000,
        maxGuests: 200,
        hoursIncluded: 12,
        details: {
          includes: [
            'Full estate access',
            'Multiple ceremony options',
            'Grand ballroom reception',
            'Premium lighting and sound',
            'All furniture and linens',
            'Luxury bridal suite',
            'Dedicated coordinator',
            'Valet parking service'
          ]
        }
      }
    ];

    for (const packageData of packages) {
      await prisma.weddingPackage.create({
        data: {
          venueId: demoVenue.id,
          ...packageData
        }
      });
    }

    console.log('✅ Wedding packages created');

    // Create event spaces
    const eventSpaces = [
      {
        name: 'Rose Garden Ceremony Space',
        description: 'Romantic outdoor ceremony space surrounded by blooming roses',
        capacity: 150,
        squareFootage: 2000,
        indoorOutdoor: 'outdoor'
      },
      {
        name: 'Grand Reception Hall',
        description: 'Elegant indoor reception space with crystal chandeliers',
        capacity: 200,
        squareFootage: 3500,
        indoorOutdoor: 'indoor'
      },
      {
        name: 'Conservatory',
        description: 'Glass-enclosed space perfect for intimate gatherings',
        capacity: 80,
        squareFootage: 1200,
        indoorOutdoor: 'both'
      }
    ];

    for (const spaceData of eventSpaces) {
      await prisma.eventSpace.create({
        data: {
          venueId: demoVenue.id,
          ...spaceData
        }
      });
    }

    console.log('✅ Event spaces created');

    // Create sample amenities
    const amenities = [
      { name: 'Bridal Suite', description: 'Luxurious preparation space for the bride', category: 'bridal' },
      { name: 'Groom\'s Room', description: 'Comfortable space for groom preparation', category: 'groom' },
      { name: 'Professional Kitchen', description: 'Full catering kitchen', category: 'catering' },
      { name: 'Sound System', description: 'Professional audio equipment', category: 'av' },
      { name: 'Lighting Package', description: 'Ambient and accent lighting', category: 'av' },
      { name: 'Parking', description: '100 car parking spaces', category: 'logistics' }
    ];

    for (const amenityData of amenities) {
      await prisma.venueAmenity.create({
        data: {
          venueId: demoVenue.id,
          ...amenityData
        }
      });
    }

    console.log('✅ Venue amenities created');

    // Create sample inquiries and leads
    const sampleInquiries = [
      {
        contactName: 'Sarah Johnson',
        contactEmail: '<EMAIL>',
        contactPhone: '******-0101',
        eventDate: new Date('2024-08-15'),
        guestCount: 120,
        eventType: 'wedding',
        budget: 12000,
        message: 'Hi! We\'re looking for a beautiful outdoor venue for our August wedding. We love gardens and are hoping to have both ceremony and reception at the same location.',
        status: 'new'
      },
      {
        contactName: 'Michael Chen',
        contactEmail: '<EMAIL>',
        contactPhone: '******-0102',
        eventDate: new Date('2024-09-22'),
        guestCount: 80,
        eventType: 'wedding',
        budget: 8000,
        message: 'We\'re planning a fall wedding and would love to schedule a tour of your venue. We\'re particularly interested in your conservatory space.',
        status: 'contacted'
      }
    ];

    for (const inquiryData of sampleInquiries) {
      const inquiry = await prisma.inquiry.create({
        data: {
          venueId: demoVenue.id,
          ...inquiryData
        }
      });

      // Create corresponding lead
      await prisma.lead.create({
        data: {
          venueId: demoVenue.id,
          inquiryId: inquiry.id,
          contactName: inquiryData.contactName,
          contactEmail: inquiryData.contactEmail,
          contactPhone: inquiryData.contactPhone,
          eventDate: inquiryData.eventDate,
          guestCount: inquiryData.guestCount,
          estimatedBudget: inquiryData.budget,
          status: 'new',
          priority: 'medium',
          source: 'website'
        }
      });
    }

    console.log('✅ Sample inquiries and leads created');

    // Create knowledge base content
    const kbCategories = [
      {
        name: 'Venue Information',
        description: 'General information about the venue'
      },
      {
        name: 'Wedding Packages',
        description: 'Details about our wedding packages'
      },
      {
        name: 'Policies',
        description: 'Venue policies and procedures'
      }
    ];

    for (const categoryData of kbCategories) {
      const category = await prisma.kBCategory.create({
        data: {
          venueId: demoVenue.id,
          ...categoryData
        }
      });

      // Add sample knowledge base items
      if (category.name === 'Venue Information') {
        await prisma.knowledgeBaseItem.create({
          data: {
            venueId: demoVenue.id,
            categoryId: category.id,
            title: 'Venue Capacity and Layout',
            content: 'Enchanted Gardens can accommodate up to 200 guests for wedding receptions. Our venue features multiple spaces including the Rose Garden ceremony area, Grand Reception Hall, and intimate Conservatory. The venue spans 5 acres of beautifully landscaped gardens.',
            contentType: 'text',
            status: 'published'
          }
        });
      }
    }

    console.log('✅ Knowledge base content created');

    console.log('🎉 Demo venue setup completed successfully!');
    console.log(`
📋 Demo Account Details:
   Email: <EMAIL>
   Password: demo123
   Venue: ${demoVenue.name}
   Slug: ${demoVenue.slug}
   
🔗 Widget URL: /widget/${demoVenue.slug}
    `);

    return {
      user: demoUser,
      venue: demoVenue
    };

  } catch (error) {
    console.error('❌ Error creating demo venue:', error);
    throw error;
  }
}

/**
 * Main function
 */
async function main() {
  try {
    await createDemoVenue();
  } catch (error) {
    console.error('❌ Demo venue creation failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  main();
}

export { createDemoVenue };
