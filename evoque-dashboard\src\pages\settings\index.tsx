import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, gql } from '@apollo/client';
import { useAuth } from '@/context/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import GoogleCalendarIntegration from '@/components/settings/GoogleCalendarIntegration';
import WidgetEmbedGenerator from '@/components/settings/WidgetEmbedGenerator';
import {
  UserIcon,
  BuildingOfficeIcon,
  KeyIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  ChatBubbleLeftRightIcon,
  CalendarIcon,
  CodeBracketIcon,
} from '@heroicons/react/24/outline';
import Link from 'next/link';

// GraphQL query for venue settings
const VENUE_QUERY = gql`
  query VenueSettings($id: ID!) {
    venue(id: $id) {
      id
      name
      description
      address {
        line1
        line2
        city
        state
        postalCode
        country
      }
      contact {
        phone
        email
        website
      }
      timezone
      settings
    }
  }
`;

// GraphQL mutation for updating venue settings
const UPDATE_VENUE_MUTATION = gql`
  mutation UpdateVenue($id: ID!, $input: UpdateVenueInput!) {
    updateVenue(id: $id, input: $input) {
      id
      name
      settings
    }
  }
`;

const Settings: React.FC = () => {
  const { currentVenue } = useAuth();
  const [activeTab, setActiveTab] = useState('general');

  const { data, loading, error } = useQuery(VENUE_QUERY, {
    variables: {
      id: currentVenue?.venue.id,
    },
    skip: !currentVenue,
  });

  const [updateVenue, { loading: updating }] = useMutation(UPDATE_VENUE_MUTATION);

  if (!currentVenue) {
    return (
      <div className="text-center py-12">
        <p className="text-lg text-gray-500">Please select a venue to continue.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-danger-50 border-l-4 border-danger-400 p-4 mb-6">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-danger-800">Error loading settings</h3>
            <div className="mt-2 text-sm text-danger-700">
              <p>{error.message}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const venue = data?.venue;

  const tabs = [
    { id: 'general', name: 'General', icon: Cog6ToothIcon },
    { id: 'profile', name: 'Profile', icon: UserIcon },
    { id: 'venue', name: 'Venue', icon: BuildingOfficeIcon },
    { id: 'users', name: 'Users', icon: UserGroupIcon },
    { id: 'security', name: 'Security', icon: KeyIcon },
    { id: 'chat', name: 'Chat Widget', icon: ChatBubbleLeftRightIcon },
    { id: 'calendar', name: 'Google Calendar', icon: CalendarIcon },
    { id: 'embed', name: 'Widget Installation', icon: CodeBracketIcon },
  ];

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Settings</h1>
        <p className="mt-1 text-sm text-gray-500">
          Manage your account and venue settings.
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
                  ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <div className="flex items-center">
                  <Icon
                    className={`-ml-0.5 mr-2 h-5 w-5 ${
                      activeTab === tab.id ? 'text-primary-500' : 'text-gray-400'
                    }`}
                    aria-hidden="true"
                  />
                  {tab.name}
                </div>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab content */}
      <div className="mt-6">
        {activeTab === 'general' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-5 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">General Settings</h3>
            </div>
            <div className="px-6 py-5">
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label htmlFor="timezone" className="block text-sm font-medium text-gray-700">
                    Timezone
                  </label>
                  <select
                    id="timezone"
                    name="timezone"
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                    defaultValue={venue.timezone || 'UTC'}
                  >
                    <option value="UTC">UTC</option>
                    <option value="America/New_York">Eastern Time (ET)</option>
                    <option value="America/Chicago">Central Time (CT)</option>
                    <option value="America/Denver">Mountain Time (MT)</option>
                    <option value="America/Los_Angeles">Pacific Time (PT)</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="date-format" className="block text-sm font-medium text-gray-700">
                    Date Format
                  </label>
                  <select
                    id="date-format"
                    name="date-format"
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                    defaultValue={venue.settings?.dateFormat || 'MM/DD/YYYY'}
                  >
                    <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                    <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                    <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="language" className="block text-sm font-medium text-gray-700">
                    Language
                  </label>
                  <select
                    id="language"
                    name="language"
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                    defaultValue={venue.settings?.language || 'en'}
                  >
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                  </select>
                </div>

                <div className="flex justify-end">
                  <button
                    type="button"
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    disabled={updating}
                  >
                    {updating ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'venue' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-5 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Venue Information</h3>
            </div>
            <div className="px-6 py-5">
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label htmlFor="venue-name" className="block text-sm font-medium text-gray-700">
                    Venue Name
                  </label>
                  <input
                    type="text"
                    name="venue-name"
                    id="venue-name"
                    className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                    defaultValue={venue.name}
                  />
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                    Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={3}
                    className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                    defaultValue={venue.description || ''}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="address-line1" className="block text-sm font-medium text-gray-700">
                      Address Line 1
                    </label>
                    <input
                      type="text"
                      name="address-line1"
                      id="address-line1"
                      className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      defaultValue={venue.address?.line1 || ''}
                    />
                  </div>

                  <div>
                    <label htmlFor="address-line2" className="block text-sm font-medium text-gray-700">
                      Address Line 2
                    </label>
                    <input
                      type="text"
                      name="address-line2"
                      id="address-line2"
                      className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      defaultValue={venue.address?.line2 || ''}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label htmlFor="city" className="block text-sm font-medium text-gray-700">
                      City
                    </label>
                    <input
                      type="text"
                      name="city"
                      id="city"
                      className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      defaultValue={venue.address?.city || ''}
                    />
                  </div>

                  <div>
                    <label htmlFor="state" className="block text-sm font-medium text-gray-700">
                      State / Province
                    </label>
                    <input
                      type="text"
                      name="state"
                      id="state"
                      className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      defaultValue={venue.address?.state || ''}
                    />
                  </div>

                  <div>
                    <label htmlFor="postal-code" className="block text-sm font-medium text-gray-700">
                      ZIP / Postal Code
                    </label>
                    <input
                      type="text"
                      name="postal-code"
                      id="postal-code"
                      className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      defaultValue={venue.address?.postalCode || ''}
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    type="button"
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    disabled={updating}
                  >
                    {updating ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'users' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-5 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Users & Permissions</h3>
              <Link
                href="/settings/users/new"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Add User
              </Link>
            </div>
            <div className="px-6 py-5">
              <p className="text-sm text-gray-500">
                Manage users and their permissions for this venue.
              </p>
              <div className="mt-4">
                <Link
                  href="/settings/users"
                  className="text-sm font-medium text-primary-600 hover:text-primary-500"
                >
                  View All Users
                </Link>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'security' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-5 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Security Settings</h3>
            </div>
            <div className="px-6 py-5">
              <div className="grid grid-cols-1 gap-6">
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="mfa"
                      name="mfa"
                      type="checkbox"
                      className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
                      defaultChecked={venue.settings?.requireMfa || false}
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="mfa" className="font-medium text-gray-700">
                      Require Multi-Factor Authentication
                    </label>
                    <p className="text-gray-500">
                      Require all users to set up multi-factor authentication for added security.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="password-policy"
                      name="password-policy"
                      type="checkbox"
                      className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
                      defaultChecked={venue.settings?.strongPasswordPolicy || false}
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="password-policy" className="font-medium text-gray-700">
                      Enforce Strong Password Policy
                    </label>
                    <p className="text-gray-500">
                      Require passwords to be at least 12 characters with a mix of letters, numbers, and symbols.
                    </p>
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    type="button"
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    disabled={updating}
                  >
                    {updating ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'chat' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-5 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Chat Widget Settings</h3>
            </div>
            <div className="px-6 py-5">
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label htmlFor="widget-title" className="block text-sm font-medium text-gray-700">
                    Widget Title
                  </label>
                  <input
                    type="text"
                    name="widget-title"
                    id="widget-title"
                    className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                    defaultValue={venue.settings?.chatWidget?.title || 'Chat with Us'}
                  />
                </div>

                <div>
                  <label htmlFor="welcome-message" className="block text-sm font-medium text-gray-700">
                    Welcome Message
                  </label>
                  <textarea
                    id="welcome-message"
                    name="welcome-message"
                    rows={3}
                    className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                    defaultValue={venue.settings?.chatWidget?.welcomeMessage || 'Hello! How can we help you today?'}
                  />
                </div>

                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="auto-open"
                      name="auto-open"
                      type="checkbox"
                      className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
                      defaultChecked={venue.settings?.chatWidget?.autoOpen || false}
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="auto-open" className="font-medium text-gray-700">
                      Auto-open Chat Widget
                    </label>
                    <p className="text-gray-500">
                      Automatically open the chat widget after 30 seconds on the page.
                    </p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Widget Color</label>
                  <div className="mt-1 flex items-center space-x-3">
                    {['#0EA5E9', '#10B981', '#8B5CF6', '#F59E0B', '#EF4444'].map((color) => (
                      <button
                        key={color}
                        type="button"
                        className={`h-8 w-8 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                        style={{ backgroundColor: color }}
                        aria-label={`Select ${color} as widget color`}
                      />
                    ))}
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    type="button"
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    disabled={updating}
                  >
                    {updating ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'profile' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-5 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Your Profile</h3>
            </div>
            <div className="px-6 py-5">
              <p className="text-sm text-gray-500">
                Manage your personal profile and account settings.
              </p>
              <div className="mt-4">
                <Link
                  href="/settings/profile"
                  className="text-sm font-medium text-primary-600 hover:text-primary-500"
                >
                  Edit Profile
                </Link>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'calendar' && (
          <GoogleCalendarIntegration venueId={venue.id} />
        )}

        {activeTab === 'embed' && venue.settings?.chatWidget && (
          <WidgetEmbedGenerator 
            venueId={venue.id}
            settings={{
              title: venue.settings.chatWidget.title || 'Chat with Us',
              welcomeMessage: venue.settings.chatWidget.welcomeMessage || 'Hello! How can we help you today?',
              primaryColor: venue.settings.chatWidget.primaryColor || '#0EA5E9',
              position: venue.settings.chatWidget.position || 'bottom-right',
              autoOpen: venue.settings.chatWidget.autoOpen || false
            }}
          />
        )}
      </div>
    </div>
  );
};

export default Settings;