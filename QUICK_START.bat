@echo off
echo ========================================
echo   Evoque Wedding Platform - Quick Start
echo ========================================
echo.

echo Checking prerequisites...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js not found!
    echo Please install Node.js 18+ from: https://nodejs.org
    echo.
    pause
    exit /b 1
) else (
    echo [OK] Node.js found
    node --version
)

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm not found!
    pause
    exit /b 1
) else (
    echo [OK] npm found
    npm --version
)

echo.
echo ========================================
echo   Installing Dependencies
echo ========================================
echo.

REM Install API dependencies
echo Installing evoque-api dependencies...
cd evoque-api
npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install evoque-api dependencies
    cd ..
    pause
    exit /b 1
)
echo [OK] evoque-api dependencies installed
cd ..

REM Install Landing dependencies
echo.
echo Installing evoque-landing dependencies...
cd evoque-landing
npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install evoque-landing dependencies
    cd ..
    pause
    exit /b 1
)
echo [OK] evoque-landing dependencies installed
cd ..

REM Install Dashboard dependencies
echo.
echo Installing evoque-dashboard dependencies...
cd evoque-dashboard
npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install evoque-dashboard dependencies
    cd ..
    pause
    exit /b 1
)
echo [OK] evoque-dashboard dependencies installed
cd ..

REM Install Widget dependencies
echo.
echo Installing evoque-widget dependencies...
cd evoque-widget
npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install evoque-widget dependencies
    cd ..
    pause
    exit /b 1
)
echo [OK] evoque-widget dependencies installed
cd ..

echo.
echo ========================================
echo   Testing Builds
echo ========================================
echo.

REM Test API build
echo Testing evoque-api build...
cd evoque-api
npm run build
if %errorlevel% neq 0 (
    echo [WARNING] evoque-api build failed - check environment variables
) else (
    echo [OK] evoque-api builds successfully
)
cd ..

REM Test Landing build
echo.
echo Testing evoque-landing build...
cd evoque-landing
npm run build
if %errorlevel% neq 0 (
    echo [WARNING] evoque-landing build failed
) else (
    echo [OK] evoque-landing builds successfully
)
cd ..

REM Test Dashboard build
echo.
echo Testing evoque-dashboard build...
cd evoque-dashboard
npm run build
if %errorlevel% neq 0 (
    echo [WARNING] evoque-dashboard build failed
) else (
    echo [OK] evoque-dashboard builds successfully
)
cd ..

REM Test Widget build
echo.
echo Testing evoque-widget build...
cd evoque-widget
npm run build
if %errorlevel% neq 0 (
    echo [WARNING] evoque-widget build failed
) else (
    echo [OK] evoque-widget builds successfully
)
cd ..

echo.
echo ========================================
echo   Setup Complete!
echo ========================================
echo.
echo Dependencies installed for all components.
echo.
echo NEXT STEPS:
echo 1. Set up environment variables (see ENVIRONMENT_SETUP_GUIDE.md)
echo 2. Configure database connection
echo 3. Set up external services (AI APIs, Google Calendar, etc.)
echo 4. Run development servers:
echo    - API: cd evoque-api && npm run dev
echo    - Landing: cd evoque-landing && npm run dev
echo    - Dashboard: cd evoque-dashboard && npm run dev
echo    - Widget: cd evoque-widget && npm run dev
echo.
echo DOCUMENTATION:
echo - Full setup guide: DEPLOYMENT_READINESS_REPORT.md
echo - Environment setup: ENVIRONMENT_SETUP_GUIDE.md
echo - VPS deployment: VPS_DEPLOYMENT_GUIDE.md
echo.
pause