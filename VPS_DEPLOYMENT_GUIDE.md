# Evoque Wedding Platform - VPS Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Evoque Wedding Platform to a VPS (Virtual Private Server) for production use.

## Prerequisites Checklist

### Local Development
- [ ] All components working locally (`npm run dev`)
- [ ] All dependencies installed
- [ ] Environment variables configured
- [ ] Database migrations tested
- [ ] All builds successful (`npm run build`)

### VPS Requirements
- [ ] VPS with Ubuntu 20.04+ or CentOS 8+
- [ ] Root or sudo access
- [ ] Domain name configured
- [ ] SSL certificate ready

### External Services
- [ ] Production database (PostgreSQL)
- [ ] Production AI API keys
- [ ] Google OAuth configured for production
- [ ] Email/SMS services configured

## Server Specifications

### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **Bandwidth**: 1TB/month
- **OS**: Ubuntu 20.04 LTS (recommended)

### Recommended for Production
- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 40GB SSD
- **Bandwidth**: Unlimited
- **Backup**: Daily automated backups

### Recommended VPS Providers
- **DigitalOcean**: $20-40/month
- **Linode**: $20-40/month
- **Vultr**: $20-40/month
- **AWS EC2**: $25-50/month
- **Google Cloud**: $25-50/month

## Phase 1: Server Setup

### 1. Initial Server Configuration

```bash
# Connect to your VPS
ssh root@your-server-ip

# Update system
apt update && apt upgrade -y

# Install essential packages
apt install -y curl wget git unzip software-properties-common

# Create non-root user
adduser evoque
usermod -aG sudo evoque

# Switch to evoque user
su - evoque
```

### 2. Install Node.js

```bash
# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version  # Should be 18.x
npm --version

# Install PM2 (Process Manager)
sudo npm install -g pm2
```

### 3. Install PostgreSQL

```bash
# Install PostgreSQL 14
sudo apt install -y postgresql postgresql-contrib

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql
```

```sql
-- In PostgreSQL shell
CREATE DATABASE evoque_prod;
CREATE USER evoque_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE evoque_prod TO evoque_user;

-- Enable vector extension
\c evoque_prod
CREATE EXTENSION IF NOT EXISTS vector;

-- Exit
\q
```

### 4. Install Nginx

```bash
# Install Nginx
sudo apt install -y nginx

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Check status
sudo systemctl status nginx
```

### 5. Configure Firewall

```bash
# Install and configure UFW
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw status
```

## Phase 2: SSL Certificate Setup

### Using Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com -d api.yourdomain.com -d dashboard.yourdomain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

### DNS Configuration

Configure these A records with your domain provider:

```
yourdomain.com          → your-server-ip
www.yourdomain.com      → your-server-ip
api.yourdomain.com      → your-server-ip
dashboard.yourdomain.com → your-server-ip
```

## Phase 3: Application Deployment

### 1. Clone Repository

```bash
# Create application directory
sudo mkdir -p /var/www/evoque
sudo chown evoque:evoque /var/www/evoque
cd /var/www/evoque

# Clone repository
git clone https://github.com/yourusername/evoque-wedding-platform.git .

# Or upload files via SCP/SFTP
```

### 2. Install Dependencies

```bash
# Install dependencies for all components
cd /var/www/evoque

# API
cd evoque-api
npm install --production
cd ..

# Landing
cd evoque-landing
npm install --production
cd ..

# Dashboard
cd evoque-dashboard
npm install --production
cd ..

# Widget
cd evoque-widget
npm install --production
cd ..
```

### 3. Environment Configuration

#### API Environment

```bash
# Create production environment file
cd /var/www/evoque/evoque-api
cp .env.example .env
nano .env
```

```env
# Production API Environment
DB_URL=postgresql://evoque_user:your_secure_password@localhost:5432/evoque_prod

# AI Services
OPENAI_API_KEY=sk-proj-your-production-openai-key
OPENROUTER_API_KEY=sk-or-v1-your-production-openrouter-key
OPENROUTER_MODEL=google/gemini-2.5-flash-lite-preview-06-17

# Google Calendar
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=https://api.yourdomain.com/auth/google/callback

# Security
JWT_SECRET=your-super-secure-jwt-secret-at-least-32-characters-long
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# Server
PORT=4000
NODE_ENV=production
LOG_LEVEL=info

# CORS
CORS_ORIGIN=https://yourdomain.com,https://dashboard.yourdomain.com

# Communication
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=+**********
RESEND_API_KEY=your_resend_key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# File Upload
MAX_FILE_SIZE=********
```

#### Frontend Environments

```bash
# Landing Page
cd /var/www/evoque/evoque-landing
cp .env.example .env.local
nano .env.local
```

```env
# Production Landing Environment
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NODE_ENV=production

# Communication (for contact forms)
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
RESEND_API_KEY=your_resend_key

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_HOTJAR_ID=1234567
```

```bash
# Dashboard
cd /var/www/evoque/evoque-dashboard
cp .env.example .env.local
nano .env.local
```

```env
# Production Dashboard Environment
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_APP_URL=https://dashboard.yourdomain.com
NODE_ENV=production

# NextAuth
NEXTAUTH_URL=https://dashboard.yourdomain.com
NEXTAUTH_SECRET=your-nextauth-secret-key

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

### 4. Database Setup

```bash
# Run database migrations
cd /var/www/evoque/evoque-api
npm run prisma:generate
npm run prisma:migrate
npm run seed
```

### 5. Build Applications

```bash
# Build API
cd /var/www/evoque/evoque-api
npm run build

# Build Landing Page
cd /var/www/evoque/evoque-landing
npm run build

# Build Dashboard
cd /var/www/evoque/evoque-dashboard
npm run build

# Build Widget
cd /var/www/evoque/evoque-widget
npm run build
```

## Phase 4: Process Management with PM2

### 1. Create PM2 Ecosystem File

```bash
cd /var/www/evoque
nano ecosystem.config.js
```

```javascript
module.exports = {
  apps: [
    {
      name: 'evoque-api',
      script: './evoque-api/dist/index.js',
      cwd: '/var/www/evoque/evoque-api',
      env: {
        NODE_ENV: 'production',
        PORT: 4000
      },
      instances: 2,
      exec_mode: 'cluster',
      max_memory_restart: '1G',
      error_file: '/var/log/evoque/api-error.log',
      out_file: '/var/log/evoque/api-out.log',
      log_file: '/var/log/evoque/api.log'
    },
    {
      name: 'evoque-landing',
      script: 'npm',
      args: 'start',
      cwd: '/var/www/evoque/evoque-landing',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      instances: 1,
      exec_mode: 'fork',
      max_memory_restart: '512M',
      error_file: '/var/log/evoque/landing-error.log',
      out_file: '/var/log/evoque/landing-out.log',
      log_file: '/var/log/evoque/landing.log'
    },
    {
      name: 'evoque-dashboard',
      script: 'npm',
      args: 'start',
      cwd: '/var/www/evoque/evoque-dashboard',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      instances: 1,
      exec_mode: 'fork',
      max_memory_restart: '512M',
      error_file: '/var/log/evoque/dashboard-error.log',
      out_file: '/var/log/evoque/dashboard-out.log',
      log_file: '/var/log/evoque/dashboard.log'
    }
  ]
};
```

### 2. Create Log Directory

```bash
sudo mkdir -p /var/log/evoque
sudo chown evoque:evoque /var/log/evoque
```

### 3. Start Applications

```bash
# Start all applications
cd /var/www/evoque
pm2 start ecosystem.config.js

# Check status
pm2 status
pm2 logs

# Save PM2 configuration
pm2 save
pm2 startup
```

## Phase 5: Nginx Configuration

### 1. Create Nginx Configuration

```bash
sudo nano /etc/nginx/sites-available/evoque
```

```nginx
# Evoque Wedding Platform - Nginx Configuration

# API Server
server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # API proxy
    location / {
        proxy_pass http://localhost:4000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # CORS headers
        add_header Access-Control-Allow-Origin "https://yourdomain.com, https://dashboard.yourdomain.com";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "https://yourdomain.com, https://dashboard.yourdomain.com";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
    }
}

# Landing Page
server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # Landing page proxy
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Static files caching
    location /_next/static/ {
        proxy_pass http://localhost:3000;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }
}

# Dashboard
server {
    listen 443 ssl http2;
    server_name dashboard.yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # Dashboard proxy
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Static files caching
    location /_next/static/ {
        proxy_pass http://localhost:3001;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }
}

# Widget CDN (serve static files)
server {
    listen 443 ssl http2;
    server_name cdn.yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    root /var/www/evoque/evoque-widget/build;
    
    # CORS for widget embedding
    add_header Access-Control-Allow-Origin "*";
    add_header Access-Control-Allow-Methods "GET, OPTIONS";
    add_header Access-Control-Allow-Headers "Content-Type";
    
    # Cache static files
    location / {
        try_files $uri $uri/ =404;
        add_header Cache-Control "public, max-age=31536000";
    }
    
    # Handle preflight requests
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type";
        add_header Content-Length 0;
        add_header Content-Type text/plain;
        return 204;
    }
}

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com api.yourdomain.com dashboard.yourdomain.com cdn.yourdomain.com;
    return 301 https://$server_name$request_uri;
}
```

### 2. Enable Site

```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/evoque /etc/nginx/sites-enabled/

# Remove default site
sudo rm /etc/nginx/sites-enabled/default

# Test configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

## Phase 6: Security Hardening

### 1. Configure Firewall

```bash
# Reset UFW
sudo ufw --force reset

# Default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow specific ports
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'

# Enable firewall
sudo ufw enable

# Check status
sudo ufw status verbose
```

### 2. Secure SSH

```bash
# Edit SSH configuration
sudo nano /etc/ssh/sshd_config
```

```
# Secure SSH settings
Port 22
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
X11Forwarding no
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2
```

```bash
# Restart SSH
sudo systemctl restart ssh
```

### 3. Set Up Fail2Ban

```bash
# Install Fail2Ban
sudo apt install -y fail2ban

# Create configuration
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
```

```bash
# Start and enable Fail2Ban
sudo systemctl start fail2ban
sudo systemctl enable fail2ban
```

## Phase 7: Monitoring and Maintenance

### 1. Set Up Log Rotation

```bash
sudo nano /etc/logrotate.d/evoque
```

```
/var/log/evoque/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 evoque evoque
    postrotate
        pm2 reloadLogs
    endscript
}
```

### 2. Create Backup Script

```bash
nano /home/<USER>/backup.sh
```

```bash
#!/bin/bash
# Evoque Platform Backup Script

BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
pg_dump -h localhost -U evoque_user evoque_prod > $BACKUP_DIR/database_$DATE.sql

# Application files backup
tar -czf $BACKUP_DIR/app_$DATE.tar.gz -C /var/www evoque

# Environment files backup
tar -czf $BACKUP_DIR/env_$DATE.tar.gz /var/www/evoque/*/.env*

# Clean old backups (keep 7 days)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

```bash
# Make executable
chmod +x /home/<USER>/backup.sh

# Add to crontab (daily backup at 2 AM)
crontab -e
```

```
0 2 * * * /home/<USER>/backup.sh >> /var/log/evoque/backup.log 2>&1
```

### 3. Health Check Script

```bash
nano /home/<USER>/health-check.sh
```

```bash
#!/bin/bash
# Evoque Platform Health Check

echo "=== Evoque Platform Health Check ==="
echo "Date: $(date)"
echo

# Check PM2 processes
echo "PM2 Processes:"
pm2 status
echo

# Check disk space
echo "Disk Usage:"
df -h /
echo

# Check memory usage
echo "Memory Usage:"
free -h
echo

# Check service status
echo "Service Status:"
sudo systemctl status nginx --no-pager -l
sudo systemctl status postgresql --no-pager -l
echo

# Check SSL certificate expiry
echo "SSL Certificate:"
echo | openssl s_client -servername yourdomain.com -connect yourdomain.com:443 2>/dev/null | openssl x509 -noout -dates
echo

# Check API health
echo "API Health:"
curl -s https://api.yourdomain.com/health || echo "API not responding"
echo

echo "=== Health Check Complete ==="
```

```bash
# Make executable
chmod +x /home/<USER>/health-check.sh

# Add to crontab (hourly check)
crontab -e
```

```
0 * * * * /home/<USER>/health-check.sh >> /var/log/evoque/health.log 2>&1
```

## Phase 8: Testing and Validation

### 1. Smoke Tests

```bash
# Test all endpoints
curl -I https://yourdomain.com
curl -I https://dashboard.yourdomain.com
curl -I https://api.yourdomain.com/health
curl -I https://cdn.yourdomain.com/evoque-widget.js

# Test SSL
ssl-cert-check -c yourdomain.com

# Test database connection
psql -h localhost -U evoque_user -d evoque_prod -c "SELECT version();"
```

### 2. Performance Tests

```bash
# Install Apache Bench
sudo apt install -y apache2-utils

# Test landing page
ab -n 100 -c 10 https://yourdomain.com/

# Test API
ab -n 100 -c 10 https://api.yourdomain.com/health
```

### 3. Security Tests

```bash
# Test SSL configuration
ssllabs-scan yourdomain.com

# Test headers
curl -I https://yourdomain.com

# Check open ports
nmap -sS yourdomain.com
```

## Deployment Checklist

### Pre-Deployment
- [ ] VPS provisioned and configured
- [ ] Domain DNS configured
- [ ] SSL certificates obtained
- [ ] All environment variables set
- [ ] Database created and migrated
- [ ] External services configured

### Deployment
- [ ] Code deployed to server
- [ ] Dependencies installed
- [ ] Applications built successfully
- [ ] PM2 processes started
- [ ] Nginx configured and running
- [ ] SSL certificates working

### Post-Deployment
- [ ] All URLs accessible
- [ ] API endpoints responding
- [ ] Database connections working
- [ ] External integrations working
- [ ] Monitoring and logging active
- [ ] Backup system configured
- [ ] Security hardening complete

### Performance Validation
- [ ] Page load times < 3 seconds
- [ ] API response times < 500ms
- [ ] SSL Labs grade A or higher
- [ ] No console errors
- [ ] Mobile responsiveness working

## Troubleshooting

### Common Issues

1. **502 Bad Gateway**
   - Check PM2 processes: `pm2 status`
   - Check application logs: `pm2 logs`
   - Verify port configurations

2. **SSL Certificate Issues**
   - Renew certificate: `sudo certbot renew`
   - Check certificate validity: `sudo certbot certificates`
   - Verify Nginx configuration: `sudo nginx -t`

3. **Database Connection Errors**
   - Check PostgreSQL status: `sudo systemctl status postgresql`
   - Verify connection string in .env
   - Check firewall rules

4. **High Memory Usage**
   - Check PM2 processes: `pm2 monit`
   - Restart applications: `pm2 restart all`
   - Check for memory leaks in logs

### Useful Commands

```bash
# PM2 Management
pm2 status
pm2 logs
pm2 restart all
pm2 reload all
pm2 monit

# Nginx Management
sudo nginx -t
sudo systemctl reload nginx
sudo systemctl status nginx

# Database Management
sudo -u postgres psql
sudo systemctl status postgresql

# System Monitoring
htop
df -h
free -h
netstat -tulpn
```

## Maintenance Schedule

### Daily
- [ ] Check application logs
- [ ] Monitor system resources
- [ ] Verify backup completion

### Weekly
- [ ] Update system packages
- [ ] Review security logs
- [ ] Check SSL certificate expiry
- [ ] Performance monitoring

### Monthly
- [ ] Security audit
- [ ] Database optimization
- [ ] Log cleanup
- [ ] Backup testing

---

**Deployment Complete!** Your Evoque Wedding Platform should now be running in production.

**Next Steps:**
1. Monitor logs for the first 24 hours
2. Set up additional monitoring (optional)
3. Configure automated backups
4. Plan for scaling as needed