import express from 'express';
import { ApolloServer } from '@apollo/server';
import { ApolloServerPluginDrainHttpServer } from '@apollo/server/plugin/drainHttpServer';
import { expressMiddleware } from '@apollo/server/express4';
import { createServer } from 'http';
import { execute, subscribe } from 'graphql';
import { SubscriptionServer } from 'subscriptions-transport-ws';
import { makeExecutableSchema } from '@graphql-tools/schema';
import cors from 'cors';
import helmet from 'helmet';
import { typeDefs } from './graphql/schema';
import { resolvers } from './graphql/resolvers';
import { directives } from './graphql/directives';
import { context } from './graphql/context';
import { formatError } from './utils/formatError';
import { testDatabaseConnection, disconnectDatabase } from './config/database';
import { env } from './config/env';
import { logger } from './utils/logger';
import widgetRoutes from './routes/widget.routes';
import { stripeRoutes } from './routes/stripe.routes';
import { WebSocketService } from './services/websocket';
import { PubSub } from 'graphql-subscriptions';

async function startServer() {
  // Create Express app
  const app = express();

  // Apply middleware
  app.use(cors({
    origin: env.CORS_ORIGIN,
    credentials: true,
  }));

  // Apply security middleware
  app.use(helmet({
    contentSecurityPolicy: env.isProd ? undefined : false,
  }));

  // Stripe webhook endpoint (must be before JSON parsing)
  app.use('/api/stripe', express.raw({ type: 'application/json' }), stripeRoutes);

  // Parse JSON request body
  app.use(express.json({ limit: '2mb' }));

  // Create HTTP server
  const httpServer = createServer(app);

  // Create PubSub instance for GraphQL subscriptions
  const pubsub = new PubSub();
  
  // Create executable schema with directives
  const schema = makeExecutableSchema({
    typeDefs,
    resolvers,
  });
  
  // Apply schema directives
  const schemaWithDirectives = directives(schema);
  
  // Create Apollo Server
  const server = new ApolloServer({
    schema: schemaWithDirectives,
    formatError,
    plugins: [
      ApolloServerPluginDrainHttpServer({ httpServer }),
      {
        async serverWillStart() {
          return {
            async drainServer() {
              subscriptionServer.close();
            },
          };
        },
      },
    ],
    introspection: !env.isProd,
  });
  
  // Create subscription server
  const subscriptionServer = SubscriptionServer.create(
    {
      schema: schemaWithDirectives,
      execute,
      subscribe,
      async onConnect(connectionParams: any) {
        // Handle authentication for subscriptions
        return { user: null }; // Replace with actual auth logic
      },
    },
    {
      server: httpServer,
      path: server.graphqlPath,
    }
  );
  
  // Test database connection
  const dbConnected = await testDatabaseConnection();
  if (!dbConnected) {
    logger.error('Failed to connect to database. Exiting...');
    process.exit(1);
  }
  
  // Start Apollo Server
  await server.start();

  // Apply Apollo middleware to Express
  app.use('/graphql', expressMiddleware(server, {
    context: async ({ req, res }) => {
      return context({ req, res });
    },
  }));
  
  // Initialize WebSocket service
  const wsService = new WebSocketService(httpServer, pubsub);

  // API routes
  app.use('/api/widget', widgetRoutes);

  // Health check endpoint
  app.get('/health', (req, res) => {
    const stats = wsService.getConnectionStats();
    res.status(200).json({
      status: 'ok',
      connections: stats,
      timestamp: new Date().toISOString()
    });
  });

  // WebSocket stats endpoint
  app.get('/api/stats/connections', (req, res) => {
    const stats = wsService.getConnectionStats();
    res.json(stats);
  });

  // Start HTTP server
  const PORT = env.PORT;
  httpServer.listen(PORT, () => {
    logger.info(`
    🚀 Server ready at http://localhost:${PORT}${server.graphqlPath}
    🔌 Subscriptions ready at ws://localhost:${PORT}${server.graphqlPath}
    💬 WebSocket ready at ws://localhost:${PORT}/dashboard and ws://localhost:${PORT}/widget
    `);
  });
  
  // Handle shutdown
  const shutdown = async () => {
    logger.info('Shutting down server...');
    await disconnectDatabase();
    process.exit(0);
  };
  
  // Handle termination signals
  process.on('SIGINT', shutdown);
  process.on('SIGTERM', shutdown);
}

// Start the server
startServer().catch((err) => {
  logger.error('Failed to start server:', err);
  process.exit(1);
});