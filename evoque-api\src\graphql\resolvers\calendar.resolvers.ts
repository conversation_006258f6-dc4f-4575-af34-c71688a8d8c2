import { calendarService } from '../../services/calendar.service';
import { logger } from '../../utils/logger';
import { Context as GraphQLContext } from '../context';
import { AuthenticationError, ForbiddenError, UserInputError } from 'apollo-server-express';

/**
 * Calendar resolvers for Google Calendar integration
 */
export const calendarResolvers = {
  // Query resolvers
  Query: {
    /**
     * Check if venue has Google Calendar connected
     */
    venueCalendarStatus: async (_: any, { venueId }: { venueId: string }, context: GraphQLContext) => {
      try {
        // Check if user has access to this venue
        if (!context.user) {
          throw new AuthenticationError('Authentication required');
        }

        const connected = await calendarService.isCalendarConnected(venueId);
        
        return {
          connected,
          connectedAt: connected ? new Date() : null, // This would come from venue settings in real implementation
          calendarId: connected ? 'primary' : null
        };
      } catch (error) {
        logger.error('Error checking calendar status:', error);
        throw new Error('Failed to check calendar status');
      }
    },

    /**
     * Get venue availability for a date range
     */
    venueAvailability: async (_: any, { venueId, startDate, endDate }: {
      venueId: string;
      startDate: string;
      endDate: string;
    }) => {
      try {
        const start = new Date(startDate);
        const end = new Date(endDate);

        // Validate date range
        if (start >= end) {
          throw new UserInputError('Start date must be before end date');
        }

        // Limit date range to prevent abuse
        const daysDiff = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
        if (daysDiff > 365) {
          throw new UserInputError('Date range cannot exceed 365 days');
        }

        const availability = await calendarService.getAvailability(venueId, start, end);
        
        return {
          available: availability.available,
          events: availability.events.map(event => ({
            id: event.id,
            summary: event.summary,
            description: event.description,
            start: event.start,
            end: event.end,
            location: null, // Could be extracted from event if needed
            attendees: [], // Could be extracted from event if needed
            isAllDay: false // Could be determined from event timing
          }))
        };
      } catch (error) {
        logger.error('Error checking venue availability:', error);
        if (error instanceof UserInputError) {
          throw error;
        }
        throw new Error('Failed to check venue availability');
      }
    }
  },

  // Mutation resolvers
  Mutation: {
    /**
     * Generate OAuth URL for venue to connect Google Calendar
     */
    generateCalendarAuthUrl: async (_: any, { venueId }: { venueId: string }, context: GraphQLContext) => {
      try {
        if (!context.user) {
          throw new AuthenticationError('Authentication required');
        }

        // TODO: Add authorization check to ensure user can manage this venue
        
        const authUrl = calendarService.generateAuthUrl(venueId);
        
        logger.info(`Calendar auth URL generated for venue: ${venueId}`);
        return authUrl;
      } catch (error) {
        logger.error('Error generating calendar auth URL:', error);
        throw new Error('Failed to generate calendar authorization URL');
      }
    },

    /**
     * Handle OAuth callback after venue authorizes access
     */
    handleCalendarAuthCallback: async (_: any, { code, venueId }: {
      code: string;
      venueId: string;
    }, context: GraphQLContext) => {
      try {
        if (!context.user) {
          throw new AuthenticationError('Authentication required');
        }

        await calendarService.handleAuthCallback(code, venueId);
        
        logger.info(`Calendar connected successfully for venue: ${venueId}`);
        return true;
      } catch (error) {
        logger.error('Error handling calendar auth callback:', error);
        throw new Error('Failed to connect Google Calendar');
      }
    },

    /**
     * Disconnect Google Calendar from venue
     */
    disconnectCalendar: async (_: any, { venueId }: { venueId: string }, context: GraphQLContext) => {
      try {
        if (!context.user) {
          throw new AuthenticationError('Authentication required');
        }

        // TODO: Add authorization check to ensure user can manage this venue
        
        await calendarService.disconnectCalendar(venueId);
        
        logger.info(`Calendar disconnected for venue: ${venueId}`);
        return true;
      } catch (error) {
        logger.error('Error disconnecting calendar:', error);
        throw new Error('Failed to disconnect Google Calendar');
      }
    },

    /**
     * Create a booking event in Google Calendar
     */
    createCalendarEvent: async (_: any, { venueId, eventDetails }: {
      venueId: string;
      eventDetails: {
        summary: string;
        description?: string;
        startTime: string;
        endTime: string;
        location?: string;
        attendeeEmail?: string;
        attendeeName?: string;
      };
    }, context: GraphQLContext) => {
      try {
        if (!context.user) {
          throw new AuthenticationError('Authentication required');
        }

        // Validate event details
        const startTime = new Date(eventDetails.startTime);
        const endTime = new Date(eventDetails.endTime);

        if (startTime >= endTime) {
          throw new UserInputError('Event start time must be before end time');
        }

        if (startTime < new Date()) {
          throw new UserInputError('Event cannot be scheduled in the past');
        }

        const eventId = await calendarService.createBookingEvent(venueId, {
          summary: eventDetails.summary,
          description: eventDetails.description || '',
          startTime,
          endTime,
          attendeeEmail: eventDetails.attendeeEmail,
          attendeeName: eventDetails.attendeeName
        });
        
        logger.info(`Calendar event created: ${eventId} for venue: ${venueId}`);
        return eventId;
      } catch (error) {
        logger.error('Error creating calendar event:', error);
        if (error instanceof UserInputError) {
          throw error;
        }
        throw new Error('Failed to create calendar event');
      }
    },

    /**
     * Send tour request notification to venue staff
     */
    sendTourRequest: async (_: any, { venueId, clientInfo }: {
      venueId: string;
      clientInfo: {
        name: string;
        email: string;
        phone?: string;
        eventDate?: string;
        guestCount?: number;
        message?: string;
      };
    }) => {
      try {
        // Validate client info
        if (!clientInfo.name || !clientInfo.email) {
          throw new UserInputError('Client name and email are required');
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(clientInfo.email)) {
          throw new UserInputError('Invalid email format');
        }

        await calendarService.notifyVenueStaff(venueId, {
          name: clientInfo.name,
          email: clientInfo.email,
          phone: clientInfo.phone,
          eventDate: clientInfo.eventDate ? new Date(clientInfo.eventDate) : undefined,
          guestCount: clientInfo.guestCount,
          message: clientInfo.message
        });
        
        logger.info(`Tour request sent for venue: ${venueId}, client: ${clientInfo.email}`);
        return true;
      } catch (error) {
        logger.error('Error sending tour request:', error);
        if (error instanceof UserInputError) {
          throw error;
        }
        throw new Error('Failed to send tour request');
      }
    }
  },

  // Field resolvers
  Venue: {
    /**
     * Resolve calendar connection status for a venue
     */
    calendarConnection: async (venue: any) => {
      try {
        const connected = await calendarService.isCalendarConnected(venue.id);
        
        return {
          connected,
          connectedAt: connected ? new Date() : null, // This would come from venue settings
          calendarId: connected ? 'primary' : null
        };
      } catch (error) {
        logger.error('Error resolving calendar connection:', error);
        return {
          connected: false,
          connectedAt: null,
          calendarId: null
        };
      }
    }
  }
};