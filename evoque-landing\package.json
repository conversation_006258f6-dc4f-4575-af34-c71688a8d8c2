{"name": "evoque-landing", "version": "0.2.0", "private": true, "scripts": {"dev": "next dev", "clean": "node scripts/clean.js", "dev:clean": "npm run clean && next dev", "build": "next build", "build:clean": "npm run clean && next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,md}\""}, "dependencies": {"@next/third-parties": "^15.3.1", "@swc/helpers": "^0.5.17", "clsx": "^2.0.0", "critters": "^0.0.23", "framer-motion": "^11.0.5", "gsap": "^3.13.0", "lottie-react": "^2.4.1", "lucide-react": "^0.309.0", "next": "^14.1.0", "openai": "^4.24.1", "react": "^18.2.0", "react-dom": "^18.2.0", "resend": "^2.1.0", "tailwind-merge": "^1.14.0", "twilio": "^4.19.3"}, "devDependencies": {"@types/node": "^20.8.9", "@types/react": "^18.2.33", "@types/react-dom": "^18.2.14", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "prettier": "^3.1.1", "tailwindcss": "^3.3.5", "typescript": "^5.3.3"}}