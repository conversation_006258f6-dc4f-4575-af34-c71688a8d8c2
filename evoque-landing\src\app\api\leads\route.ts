import { NextRequest, NextResponse } from 'next/server';
// API endpoint for the backend
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';

// Mock lead scoring function (replace with actual implementation)
async function scoreLead(leadData: any) {
  const factors = {
    budgetMatch: 0,
    dateAvailability: 0,
    guestCountFit: 0,
    engagement: 0,
    timing: 0
  };

  // Budget scoring
  const budget = parseInt(leadData.eventDetails?.budget?.replace(/\D/g, '') || '0');
  if (budget >= 10000 && budget <= 25000) factors.budgetMatch = 25;
  else if (budget >= 5000) factors.budgetMatch = 18;
  else factors.budgetMatch = 10;

  // Date availability (mock - in real app, check against bookings)
  factors.dateAvailability = 20;

  // Guest count fit (assuming venue capacity 50-300)
  const guests = leadData.eventDetails?.guestCount || 0;
  if (guests >= 100 && guests <= 200) factors.guestCountFit = 20;
  else if (guests >= 50 && guests <= 300) factors.guestCountFit = 15;
  else factors.guestCountFit = 5;

  // Engagement scoring
  const messageLength = leadData.inquiryData?.message?.length || 0;
  factors.engagement = Math.min(messageLength / 20, 20);

  // Timing scoring
  const eventDate = new Date(leadData.eventDetails?.date || '');
  const daysUntil = Math.floor((eventDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
  if (daysUntil >= 180 && daysUntil <= 365) factors.timing = 10;
  else if (daysUntil >= 120) factors.timing = 7;
  else factors.timing = 4;

  const totalScore = Object.values(factors).reduce((sum, score) => sum + score, 0);
  
  return {
    score: totalScore,
    factors,
    recommendation: totalScore >= 80 ? 'HOT' : totalScore >= 50 ? 'WARM' : 'COLD',
    insights: generateInsights(factors, totalScore)
  };
}

function generateInsights(factors: any, score: number) {
  const insights = [];
  
  if (factors.budgetMatch >= 20) {
    insights.push('💰 Budget aligns perfectly with your pricing');
  }
  if (factors.dateAvailability >= 15) {
    insights.push('📅 Requested date is available');
  }
  if (factors.engagement >= 15) {
    insights.push('🔥 Highly engaged prospect');
  }
  if (score >= 80) {
    insights.push('⚡ Priority lead - respond immediately!');
  }
  
  return insights;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Score the lead
    const scoreResult = await scoreLead(body);
    
    // Save to database via API
    let lead = null;
    try {
      const response = await fetch(`${API_BASE_URL}/api/leads`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          venueId: body.venueId || 'demo-venue-id',
          contactInfo: {
            name: body.contactInfo?.name,
            email: body.contactInfo?.email,
            phone: body.contactInfo?.phone
          },
          eventDetails: body.eventDetails,
          score: scoreResult.score,
          scoreFactors: scoreResult.factors,
          status: scoreResult.recommendation,
          source: body.source || 'website'
        })
      });
      
      if (response.ok) {
        lead = await response.json();
      }
    } catch (error) {
      console.error('API error:', error);
      // Continue even if database save fails for demo
    }

    // If it's a HOT lead, trigger notifications (mock for now)
    if (scoreResult.recommendation === 'HOT') {
      // In production, trigger SMS/email notifications here
      console.log('🔥 HOT lead detected! Triggering notifications...');
    }

    return NextResponse.json({
      success: true,
      leadId: lead?.id || 'demo-' + Date.now(),
      score: scoreResult,
      message: 'Lead scored and saved successfully'
    });

  } catch (error) {
    console.error('Lead scoring error:', error);
    return NextResponse.json(
      { error: 'Failed to process lead' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const venueId = searchParams.get('venueId');
  const status = searchParams.get('status');

  try {
    const queryParams = new URLSearchParams();
    if (venueId) queryParams.append('venueId', venueId);
    if (status && status !== 'all') queryParams.append('status', status.toUpperCase());
    queryParams.append('limit', '50');

    const response = await fetch(`${API_BASE_URL}/api/leads?${queryParams}`);
    
    if (!response.ok) {
      console.error('API error:', response.statusText);
      // Return mock data for demo
      return NextResponse.json({
        leads: [
          {
            id: '1',
            contactInfo: { name: 'Sarah & Michael', email: '<EMAIL>' },
            eventDetails: { date: '2025-10-15', guestCount: 150 },
            score: 92,
            status: 'HOT',
            createdAt: new Date().toISOString()
          }
        ]
      });
    }

    const leads = await response.json();
    return NextResponse.json({ leads: leads || [] });

  } catch (error) {
    console.error('Error fetching leads:', error);
    return NextResponse.json(
      { error: 'Failed to fetch leads' },
      { status: 500 }
    );
  }
}