import { gql } from 'apollo-server-express';

export const calendarTypeDefs = gql`
  # Calendar types
  type CalendarEvent {
    id: ID!
    summary: String!
    description: String
    start: DateTime!
    end: DateTime!
    location: String
    attendees: [String]
    isAllDay: Boolean
  }

  type CalendarAvailability {
    available: Boolean!
    events: [CalendarEvent!]
  }

  type CalendarConnection {
    connected: Boolean!
    connectedAt: DateTime
    calendarId: String
  }

  # Input types
  input CalendarEventInput {
    summary: String!
    description: String
    startTime: DateTime!
    endTime: DateTime!
    location: String
    attendeeEmail: String
    attendeeName: String
  }

  input TourRequestInput {
    name: String!
    email: String!
    phone: String
    eventDate: DateTime
    guestCount: Int
    message: String
  }

  # Extend existing types
  extend type Venue {
    calendarConnection: CalendarConnection
  }

  # Queries
  extend type Query {
    # Check if venue has Google Calendar connected
    venueCalendarStatus(venueId: ID!): CalendarConnection!
    
    # Get venue availability for a date range
    venueAvailability(venueId: ID!, startDate: DateTime!, endDate: DateTime!): CalendarAvailability!
  }

  # Mutations
  extend type Mutation {
    # Generate OAuth URL for venue to connect Google Calendar
    generateCalendarAuthUrl(venueId: ID!): String!
    
    # Handle OAuth callback after venue authorizes access
    handleCalendarAuthCallback(code: String!, venueId: ID!): Boolean!
    
    # Disconnect Google Calendar from venue
    disconnectCalendar(venueId: ID!): Boolean!
    
    # Create a booking event in Google Calendar
    createCalendarEvent(venueId: ID!, eventDetails: CalendarEventInput!): ID
    
    # Send tour request notification to venue staff
    sendTourRequest(venueId: ID!, clientInfo: TourRequestInput!): Boolean!
  }
`;