# Evoque Wedding Platform - Deployment Readiness Report

## Executive Summary

**Status: ❌ NOT READY FOR DEPLOYMENT**

The Evoque Wedding Platform requires significant setup and configuration before it can be deployed. This report identifies critical issues and provides a comprehensive roadmap for achieving deployment readiness.

## Phase 1: Critical Issues Identified

### 🚨 Immediate Blockers

1. **Missing Dependencies**: No `node_modules` directories found in any component
2. **Webpack Configuration Error**: Missing `path` import in `evoque-widget/webpack.config.js`
3. **Environment Variables**: No `.env` files configured
4. **Database**: PostgreSQL database not set up locally

### 📦 Component Status

| Component | Dependencies | Config Issues | Status |
|-----------|-------------|---------------|--------|
| evoque-api | ❌ Not installed | ✅ Good | ❌ Blocked |
| evoque-landing | ❌ Not installed | ✅ Good | ❌ Blocked |
| evoque-dashboard | ❌ Not installed | ✅ Good | ❌ Blocked |
| evoque-widget | ❌ Not installed | ❌ Webpack error | ❌ Blocked |

## Phase 2: Dependency Installation & Build Fixes

### Required Actions

#### 1. Fix Webpack Configuration
```javascript
// evoque-widget/webpack.config.js - Add missing import
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
```

#### 2. Install Dependencies (Run in each directory)
```bash
# evoque-api
cd evoque-api
npm install

# evoque-landing  
cd ../evoque-landing
npm install

# evoque-dashboard
cd ../evoque-dashboard
npm install

# evoque-widget
cd ../evoque-widget
npm install
```

#### 3. Expected Installation Issues
- **Node.js Version**: Requires Node.js >=18.0.0
- **Python Dependencies**: Some packages may require Python/Visual Studio Build Tools
- **Prisma**: Will need database connection for generation

## Phase 3: Environment Configuration Analysis

### Critical Environment Variables

#### 🔴 **CRITICAL - Required for Basic Functionality**

**Database (evoque-api)**
```env
DB_URL=postgresql://username:password@host:port/database
```

**API Configuration**
```env
NEXT_PUBLIC_API_URL=http://localhost:4000
API_BASE_URL=http://localhost:4000
PORT=4000
JWT_SECRET=your-secure-jwt-secret
```

#### 🟡 **REQUIRED - For Full Features**

**AI Services**
```env
# OpenAI for embeddings
OPENAI_API_KEY=sk-proj-your-openai-key

# OpenRouter for chat (recommended)
OPENROUTER_API_KEY=sk-or-v1-your-key
OPENROUTER_MODEL=google/gemini-2.5-flash-lite-preview-06-17

# Alternative: Perplexity
PERPLEXITY_API_KEY=pplx-your-key
```

**Google Calendar Integration**
```env
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:4000/auth/google/callback
```

#### 🟢 **OPTIONAL - Enhanced Features**

**Communication Services**
```env
# Twilio for SMS
TWILIO_ACCOUNT_SID=your-sid
TWILIO_AUTH_TOKEN=your-token
TWILIO_PHONE_NUMBER=+**********

# Resend for Email
RESEND_API_KEY=your-resend-key
```

**Payment Processing (Future)**
```env
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
```

**Analytics (Optional)**
```env
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_HOTJAR_ID=1234567
```

## Phase 4: External Service Requirements

### 🔴 Critical Services (Account Setup Required)

#### 1. **PostgreSQL Database**
- **Options**: 
  - Local PostgreSQL installation
  - Supabase (recommended for development)
  - Render.com PostgreSQL
  - AWS RDS
- **Requirements**: 
  - PostgreSQL 14+
  - Vector extension support
  - Connection string format: `postgresql://user:pass@host:port/db`

#### 2. **AI Service Provider**
- **OpenAI** (required for embeddings)
  - Sign up: https://platform.openai.com
  - Minimum: $5 credit for testing
  - API key format: `sk-proj-...`

- **OpenRouter** (recommended for chat)
  - Sign up: https://openrouter.ai
  - More cost-effective than direct OpenAI
  - API key format: `sk-or-v1-...`

### 🟡 Important Services (Full Functionality)

#### 3. **Google Cloud Console** (Calendar Integration)
- Create project at https://console.cloud.google.com
- Enable Google Calendar API
- Create OAuth 2.0 credentials
- Configure authorized redirect URIs

#### 4. **Communication Services**
- **Twilio** (SMS): https://www.twilio.com
- **Resend** (Email): https://resend.com

### 🟢 Optional Services (Enhanced Features)

#### 5. **Payment Processing**
- **Stripe**: https://stripe.com
- Test mode available for development

#### 6. **Analytics**
- **Google Analytics**: https://analytics.google.com
- **Hotjar**: https://www.hotjar.com

## Phase 5: VPS Deployment Checklist

### Server Requirements

#### Minimum Specifications
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **OS**: Ubuntu 20.04+ or CentOS 8+

#### Software Requirements
```bash
# Node.js (required version)
node --version  # Should be >= 18.0.0
npm --version

# PostgreSQL
psql --version  # Should be >= 14

# PM2 (process manager)
npm install -g pm2

# Nginx (reverse proxy)
nginx -v
```

### Pre-Deployment Setup

#### 1. **Domain & SSL**
- [ ] Domain name configured
- [ ] DNS A records pointing to VPS IP
- [ ] SSL certificate (Let's Encrypt recommended)

#### 2. **Environment Files**
```bash
# Create production environment files
cp .env.example .env.production
# Configure all required variables
```

#### 3. **Database Setup**
```bash
# Run Prisma migrations
npm run prisma:migrate
npm run prisma:generate
npm run seed
```

#### 4. **Build Applications**
```bash
# Build all components
cd evoque-api && npm run build
cd ../evoque-landing && npm run build
cd ../evoque-dashboard && npm run build
cd ../evoque-widget && npm run build
```

#### 5. **Security Configuration**
- [ ] Firewall configured (ports 80, 443, 22)
- [ ] SSH key authentication
- [ ] Regular security updates
- [ ] Environment variables secured
- [ ] Database access restricted

### Production Environment Variables Template

```env
# =================================
# PRODUCTION ENVIRONMENT VARIABLES
# =================================

# Database (CRITICAL)
DB_URL=postgresql://prod_user:secure_password@localhost:5432/evoque_prod

# API Configuration (CRITICAL)
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
API_BASE_URL=https://api.yourdomain.com
PORT=4000
NODE_ENV=production

# Security (CRITICAL)
JWT_SECRET=your-super-secure-jwt-secret-min-32-chars
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# AI Services (REQUIRED)
OPENAI_API_KEY=sk-proj-your-production-openai-key
OPENROUTER_API_KEY=sk-or-v1-your-production-openrouter-key
OPENROUTER_MODEL=google/gemini-2.5-flash-lite-preview-06-17

# Google Calendar (REQUIRED for booking)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=https://api.yourdomain.com/auth/google/callback

# Communication (RECOMMENDED)
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
TWILIO_PHONE_NUMBER=+**********
RESEND_API_KEY=your-resend-key

# CORS & Security
CORS_ORIGIN=https://yourdomain.com,https://dashboard.yourdomain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# File Upload
MAX_FILE_SIZE=********

# Logging
LOG_LEVEL=info
```

## Implementation Roadmap

### Immediate Actions (Day 1)
1. ✅ Fix webpack configuration error
2. ✅ Install all dependencies
3. ✅ Set up local PostgreSQL database
4. ✅ Configure basic environment variables
5. ✅ Test `npm run dev` for all components

### Short-term (Week 1)
1. ✅ Set up external service accounts
2. ✅ Configure AI API keys
3. ✅ Test Google Calendar integration
4. ✅ Verify all features working locally

### Medium-term (Week 2-3)
1. ✅ Prepare production environment
2. ✅ Configure VPS server
3. ✅ Set up domain and SSL
4. ✅ Deploy and test in production

### Long-term (Month 1+)
1. ✅ Monitor performance and errors
2. ✅ Implement additional features
3. ✅ Scale infrastructure as needed
4. ✅ Set up backup and monitoring

## Risk Assessment

### High Risk
- **Database Configuration**: Complex setup with vector extensions
- **AI API Costs**: Can escalate quickly without proper monitoring
- **Google OAuth**: Requires careful configuration for production

### Medium Risk
- **Dependency Conflicts**: Multiple complex packages
- **Environment Variables**: Easy to misconfigure
- **SSL Certificate**: Renewal and configuration

### Low Risk
- **Basic functionality**: Well-structured codebase
- **Frontend deployment**: Standard Next.js applications
- **Widget embedding**: Self-contained component

## Success Metrics

### Development Ready
- [ ] All `npm run dev` commands work
- [ ] All components start without errors
- [ ] Database connections established
- [ ] AI services responding

### Production Ready
- [ ] All builds complete successfully
- [ ] SSL certificates configured
- [ ] All external services connected
- [ ] Performance benchmarks met
- [ ] Security audit passed

## Next Steps

1. **Fix immediate blockers** (webpack config, dependencies)
2. **Set up development environment** (database, API keys)
3. **Test all components locally**
4. **Prepare production environment**
5. **Deploy and monitor**

---

**Report Generated**: $(Get-Date)
**Platform**: Evoque Wedding Platform
**Components**: API, Landing, Dashboard, Widget
**Status**: Requires immediate attention before deployment