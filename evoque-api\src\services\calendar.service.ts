import { google, calendar_v3 } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { prisma } from '../lib/prisma';
import { logger } from '../utils/logger';
import { env } from '../config/env';

/**
 * Google Calendar Service for venue booking integration
 * Handles OAuth authentication, calendar access, and booking management
 */
export class CalendarService {
  private oauth2Client: OAuth2Client;
  private calendar: calendar_v3.Calendar;

  constructor() {
    this.oauth2Client = new google.auth.OAuth2(
      env.GOOGLE_CLIENT_ID,
      env.GOOGLE_CLIENT_SECRET,
      env.GOOGLE_REDIRECT_URI
    );
    
    this.calendar = google.calendar({ version: 'v3', auth: this.oauth2Client });
  }

  /**
   * Generate OAuth URL for venue staff to connect their Google Calendar
   */
  generateAuthUrl(venueId: string): string {
    const scopes = [
      'https://www.googleapis.com/auth/calendar.readonly',
      'https://www.googleapis.com/auth/calendar.events'
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      state: venueId, // Pass venue ID to identify which venue is connecting
      prompt: 'consent' // Force consent to get refresh token
    });
  }

  /**
   * Exchange authorization code for tokens and store them
   */
  async handleAuthCallback(code: string, venueId: string): Promise<void> {
    try {
      const { tokens } = await this.oauth2Client.getAccessToken(code);
      
      if (!tokens.access_token) {
        throw new Error('Failed to obtain access token');
      }

      // Store tokens in database
      await prisma.venue.update({
        where: { id: venueId },
        data: {
          settings: {
            ...await this.getVenueSettings(venueId),
            googleCalendar: {
              accessToken: tokens.access_token,
              refreshToken: tokens.refresh_token,
              expiryDate: tokens.expiry_date,
              connected: true,
              connectedAt: new Date().toISOString()
            }
          }
        }
      });

      logger.info(`Google Calendar connected for venue: ${venueId}`);
    } catch (error) {
      logger.error('Error handling calendar auth callback:', error);
      throw new Error('Failed to connect Google Calendar');
    }
  }

  /**
   * Check if venue has Google Calendar connected
   */
  async isCalendarConnected(venueId: string): Promise<boolean> {
    const venue = await prisma.venue.findUnique({
      where: { id: venueId },
      select: { settings: true }
    });

    const settings = venue?.settings as any;
    return settings?.googleCalendar?.connected === true;
  }

  /**
   * Get venue availability for a specific date range
   */
  async getAvailability(venueId: string, startDate: Date, endDate: Date): Promise<{
    available: boolean;
    events: Array<{
      id: string;
      summary: string;
      start: Date;
      end: Date;
      description?: string;
    }>;
  }> {
    try {
      // Set up authentication for this venue
      await this.setupVenueAuth(venueId);

      // Get events from Google Calendar
      const response = await this.calendar.events.list({
        calendarId: 'primary',
        timeMin: startDate.toISOString(),
        timeMax: endDate.toISOString(),
        singleEvents: true,
        orderBy: 'startTime'
      });

      const events = response.data.items?.map(event => ({
        id: event.id!,
        summary: event.summary || 'Untitled Event',
        start: new Date(event.start?.dateTime || event.start?.date || ''),
        end: new Date(event.end?.dateTime || event.end?.date || ''),
        description: event.description
      })) || [];

      // Check if the requested time slot conflicts with existing events
      const hasConflict = events.some(event => {
        return (startDate < event.end && endDate > event.start);
      });

      return {
        available: !hasConflict,
        events
      };
    } catch (error) {
      logger.error('Error checking calendar availability:', error);
      
      // Fallback to database availability if Google Calendar fails
      return await this.getDatabaseAvailability(venueId, startDate, endDate);
    }
  }

  /**
   * Create a booking event in Google Calendar
   */
  async createBookingEvent(venueId: string, eventDetails: {
    summary: string;
    description: string;
    startTime: Date;
    endTime: Date;
    attendeeEmail?: string;
    attendeeName?: string;
  }): Promise<string | null> {
    try {
      await this.setupVenueAuth(venueId);

      const event: calendar_v3.Schema$Event = {
        summary: eventDetails.summary,
        description: eventDetails.description,
        start: {
          dateTime: eventDetails.startTime.toISOString(),
        },
        end: {
          dateTime: eventDetails.endTime.toISOString(),
        },
        attendees: eventDetails.attendeeEmail ? [{
          email: eventDetails.attendeeEmail,
          displayName: eventDetails.attendeeName
        }] : undefined,
        reminders: {
          useDefault: false,
          overrides: [
            { method: 'email', minutes: 24 * 60 }, // 1 day before
            { method: 'popup', minutes: 60 } // 1 hour before
          ]
        }
      };

      const response = await this.calendar.events.insert({
        calendarId: 'primary',
        requestBody: event,
        sendUpdates: 'all'
      });

      logger.info(`Calendar event created: ${response.data.id} for venue: ${venueId}`);
      return response.data.id || null;
    } catch (error) {
      logger.error('Error creating calendar event:', error);
      return null;
    }
  }

  /**
   * Send tour request notification to venue staff
   */
  async notifyVenueStaff(venueId: string, clientInfo: {
    name: string;
    email: string;
    phone?: string;
    eventDate?: Date;
    guestCount?: number;
    message?: string;
  }): Promise<void> {
    try {
      const venue = await prisma.venue.findUnique({
        where: { id: venueId },
        include: {
          venueUsers: {
            include: { user: true }
          }
        }
      });

      if (!venue) {
        throw new Error('Venue not found');
      }

      // Create a calendar event for the venue staff as a reminder to follow up
      const followUpDate = new Date();
      followUpDate.setHours(followUpDate.getHours() + 2); // Follow up in 2 hours

      const eventSummary = `Follow up: ${clientInfo.name} - Tour Request`;
      const eventDescription = `
Client Information:
- Name: ${clientInfo.name}
- Email: ${clientInfo.email}
- Phone: ${clientInfo.phone || 'Not provided'}
- Event Date: ${clientInfo.eventDate?.toLocaleDateString() || 'Not specified'}
- Guest Count: ${clientInfo.guestCount || 'Not specified'}
- Message: ${clientInfo.message || 'No additional message'}

Please contact the client to schedule a tour and discuss their event needs.`;

      await this.createBookingEvent(venueId, {
        summary: eventSummary,
        description: eventDescription,
        startTime: followUpDate,
        endTime: new Date(followUpDate.getTime() + 30 * 60 * 1000), // 30 minutes
        attendeeEmail: clientInfo.email,
        attendeeName: clientInfo.name
      });

      logger.info(`Staff notification created for venue: ${venueId}, client: ${clientInfo.email}`);
    } catch (error) {
      logger.error('Error notifying venue staff:', error);
    }
  }

  /**
   * Disconnect Google Calendar for a venue
   */
  async disconnectCalendar(venueId: string): Promise<void> {
    try {
      await prisma.venue.update({
        where: { id: venueId },
        data: {
          settings: {
            ...await this.getVenueSettings(venueId),
            googleCalendar: {
              connected: false,
              disconnectedAt: new Date().toISOString()
            }
          }
        }
      });

      logger.info(`Google Calendar disconnected for venue: ${venueId}`);
    } catch (error) {
      logger.error('Error disconnecting calendar:', error);
      throw new Error('Failed to disconnect Google Calendar');
    }
  }

  /**
   * Private helper methods
   */
  private async setupVenueAuth(venueId: string): Promise<void> {
    const settings = await this.getVenueSettings(venueId);
    const googleCalendar = settings?.googleCalendar;

    if (!googleCalendar?.connected || !googleCalendar?.accessToken) {
      throw new Error('Google Calendar not connected for this venue');
    }

    this.oauth2Client.setCredentials({
      access_token: googleCalendar.accessToken,
      refresh_token: googleCalendar.refreshToken,
      expiry_date: googleCalendar.expiryDate
    });

    // Check if token needs refresh
    if (googleCalendar.expiryDate && new Date() > new Date(googleCalendar.expiryDate)) {
      await this.refreshTokens(venueId);
    }
  }

  private async refreshTokens(venueId: string): Promise<void> {
    try {
      const { credentials } = await this.oauth2Client.refreshAccessToken();
      
      await prisma.venue.update({
        where: { id: venueId },
        data: {
          settings: {
            ...await this.getVenueSettings(venueId),
            googleCalendar: {
              ...((await this.getVenueSettings(venueId))?.googleCalendar || {}),
              accessToken: credentials.access_token,
              refreshToken: credentials.refresh_token,
              expiryDate: credentials.expiry_date
            }
          }
        }
      });

      logger.info(`Tokens refreshed for venue: ${venueId}`);
    } catch (error) {
      logger.error('Error refreshing tokens:', error);
      throw new Error('Failed to refresh calendar tokens');
    }
  }

  private async getVenueSettings(venueId: string): Promise<any> {
    const venue = await prisma.venue.findUnique({
      where: { id: venueId },
      select: { settings: true }
    });
    return venue?.settings || {};
  }

  private async getDatabaseAvailability(venueId: string, startDate: Date, endDate: Date): Promise<{
    available: boolean;
    events: Array<{
      id: string;
      summary: string;
      start: Date;
      end: Date;
      description?: string;
    }>;
  }> {
    // Fallback to check database availability
    const availability = await prisma.venueAvailability.findMany({
      where: {
        venueId,
        dayOfWeek: {
          in: this.getDaysOfWeekInRange(startDate, endDate)
        }
      }
    });

    const isAvailable = availability.some(avail => avail.isAvailable);

    return {
      available: isAvailable,
      events: [] // No events from database fallback
    };
  }

  private getDaysOfWeekInRange(startDate: Date, endDate: Date): number[] {
    const days: number[] = [];
    const current = new Date(startDate);
    
    while (current <= endDate) {
      days.push(current.getDay());
      current.setDate(current.getDate() + 1);
    }
    
    return [...new Set(days)];
  }
}

// Export singleton instance
export const calendarService = new CalendarService();