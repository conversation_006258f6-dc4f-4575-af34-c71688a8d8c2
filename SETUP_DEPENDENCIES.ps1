# Evoque Wedding Platform - Dependency Setup Script
# This script installs all required dependencies and fixes common issues

Write-Host "🚀 Evoque Wedding Platform - Dependency Setup" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

# Check if we're in the right directory
if (-not (Test-Path "evoque-api") -or -not (Test-Path "evoque-landing")) {
    Write-Host "❌ Please run this script from the Evoque-Wed root directory" -ForegroundColor Red
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
    exit 1
}

# Check Node.js version
Write-Host "`n📋 Checking Prerequisites..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
    
    # Extract version number and check if >= 18
    $versionNumber = [version]($nodeVersion -replace 'v', '')
    if ($versionNumber.Major -lt 18) {
        Write-Host "❌ Node.js version 18+ required. Current: $nodeVersion" -ForegroundColor Red
        Write-Host "Please update Node.js: https://nodejs.org" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js 18+" -ForegroundColor Red
    Write-Host "Download from: https://nodejs.org" -ForegroundColor Yellow
    exit 1
}

try {
    $npmVersion = npm --version
    Write-Host "✅ npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm not found" -ForegroundColor Red
    exit 1
}

# Function to install dependencies with error handling
function Install-Dependencies {
    param(
        [string]$ComponentName,
        [string]$Path
    )
    
    Write-Host "`n📦 Installing dependencies for $ComponentName..." -ForegroundColor Yellow
    Write-Host "Directory: $Path" -ForegroundColor Gray
    
    if (-not (Test-Path $Path)) {
        Write-Host "❌ Directory not found: $Path" -ForegroundColor Red
        return $false
    }
    
    Push-Location $Path
    
    try {
        # Clear npm cache if needed
        Write-Host "Clearing npm cache..." -ForegroundColor Gray
        npm cache clean --force 2>$null
        
        # Install dependencies
        Write-Host "Running npm install..." -ForegroundColor Gray
        $result = npm install 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $ComponentName dependencies installed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Failed to install $ComponentName dependencies" -ForegroundColor Red
            Write-Host "Error output:" -ForegroundColor Yellow
            Write-Host $result -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Exception during $ComponentName installation: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    } finally {
        Pop-Location
    }
}

# Function to test build
function Test-Build {
    param(
        [string]$ComponentName,
        [string]$Path,
        [string]$BuildCommand = "build"
    )
    
    Write-Host "`n🔨 Testing build for $ComponentName..." -ForegroundColor Yellow
    
    Push-Location $Path
    
    try {
        $result = npm run $BuildCommand 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $ComponentName builds successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $ComponentName build failed" -ForegroundColor Red
            Write-Host "Build output:" -ForegroundColor Yellow
            Write-Host $result -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Exception during $ComponentName build: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    } finally {
        Pop-Location
    }
}

# Install dependencies for each component
$components = @(
    @{Name="evoque-api"; Path="evoque-api"},
    @{Name="evoque-landing"; Path="evoque-landing"},
    @{Name="evoque-dashboard"; Path="evoque-dashboard"},
    @{Name="evoque-widget"; Path="evoque-widget"}
)

$installResults = @{}

foreach ($component in $components) {
    $installResults[$component.Name] = Install-Dependencies -ComponentName $component.Name -Path $component.Path
}

# Test builds (skip if dependencies failed)
Write-Host "`n🔨 Testing Builds..." -ForegroundColor Cyan

$buildResults = @{}

foreach ($component in $components) {
    if ($installResults[$component.Name]) {
        if ($component.Name -eq "evoque-api") {
            # API uses TypeScript compilation
            $buildResults[$component.Name] = Test-Build -ComponentName $component.Name -Path $component.Path -BuildCommand "build"
        } else {
            # Frontend components use Next.js/Webpack build
            $buildResults[$component.Name] = Test-Build -ComponentName $component.Name -Path $component.Path -BuildCommand "build"
        }
    } else {
        Write-Host "⏭️  Skipping build test for $($component.Name) (dependencies failed)" -ForegroundColor Yellow
        $buildResults[$component.Name] = $false
    }
}

# Summary Report
Write-Host "`n📊 Installation Summary" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

foreach ($component in $components) {
    $name = $component.Name
    $depStatus = if ($installResults[$name]) { "✅" } else { "❌" }
    $buildStatus = if ($buildResults[$name]) { "✅" } else { "❌" }
    
    Write-Host "$name:" -ForegroundColor White
    Write-Host "  Dependencies: $depStatus" -ForegroundColor $(if ($installResults[$name]) { "Green" } else { "Red" })
    Write-Host "  Build Test:   $buildStatus" -ForegroundColor $(if ($buildResults[$name]) { "Green" } else { "Red" })
}

# Check for common issues
Write-Host "`n🔍 Common Issues Check" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan

# Check for Python (needed for some native modules)
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python available: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Python not found - some native modules may fail to install" -ForegroundColor Yellow
    Write-Host "   Install Python from: https://www.python.org/downloads/" -ForegroundColor Gray
}

# Check for Visual Studio Build Tools (Windows)
if ($env:OS -eq "Windows_NT") {
    $vsWhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
    if (Test-Path $vsWhere) {
        Write-Host "✅ Visual Studio Build Tools detected" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Visual Studio Build Tools not found" -ForegroundColor Yellow
        Write-Host "   Some native modules may fail to compile" -ForegroundColor Gray
        Write-Host "   Install from: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022" -ForegroundColor Gray
    }
}

# Next Steps
Write-Host "`n🎯 Next Steps" -ForegroundColor Cyan
Write-Host "============" -ForegroundColor Cyan

$allSuccess = ($installResults.Values | Where-Object { $_ -eq $false }).Count -eq 0

if ($allSuccess) {
    Write-Host "✅ All dependencies installed successfully!" -ForegroundColor Green
    Write-Host "`nYou can now:" -ForegroundColor White
    Write-Host "1. Set up environment variables (see DEPLOYMENT_READINESS_REPORT.md)" -ForegroundColor Gray
    Write-Host "2. Configure database connection" -ForegroundColor Gray
    Write-Host "3. Run development servers with 'npm run dev' in each component" -ForegroundColor Gray
} else {
    Write-Host "❌ Some components failed to install" -ForegroundColor Red
    Write-Host "`nTroubleshooting:" -ForegroundColor White
    Write-Host "1. Check Node.js version (requires 18+)" -ForegroundColor Gray
    Write-Host "2. Install Python and Visual Studio Build Tools" -ForegroundColor Gray
    Write-Host "3. Clear npm cache: npm cache clean --force" -ForegroundColor Gray
    Write-Host "4. Try installing components individually" -ForegroundColor Gray
    Write-Host "5. Check network connectivity and npm registry access" -ForegroundColor Gray
}

Write-Host "`n📚 Documentation:" -ForegroundColor White
Write-Host "- Full setup guide: DEPLOYMENT_READINESS_REPORT.md" -ForegroundColor Gray
Write-Host "- Environment setup: .env.example files in each component" -ForegroundColor Gray
Write-Host "- API documentation: evoque-api/README.md" -ForegroundColor Gray

Write-Host "`n🏁 Setup Complete!" -ForegroundColor Green