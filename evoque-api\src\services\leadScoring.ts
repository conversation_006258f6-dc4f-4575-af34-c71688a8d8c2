// Lead Scoring Service - The Heart of Evoque's Value Proposition
// This service qualifies leads to save venues time and increase conversions

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface LeadData {
  contactInfo: {
    name: string;
    email: string;
    phone?: string;
  };
  eventDetails: {
    date: string;
    guestCount: number;
    budget?: string;
    eventType: 'wedding' | 'reception' | 'ceremony' | 'other';
  };
  inquiryData: {
    message: string;
    source: string;
    timestamp: Date;
    pagesViewed: string[];
    timeOnSite: number;
    returnVisit: boolean;
  };
}

export interface LeadScore {
  score: number; // 0-100
  factors: {
    budgetMatch: number;      // 0-25 points
    dateAvailability: number; // 0-25 points
    guestCountFit: number;    // 0-20 points
    engagement: number;       // 0-20 points
    timing: number;          // 0-10 points
  };
  recommendation: 'HOT' | 'WARM' | 'COLD';
  insights: string[];
}

export class LeadScoringService {
  private venueId: string;
  private venueData: any;

  constructor(venueId: string) {
    this.venueId = venueId;
  }

  async initialize() {
    const venue = await prisma.venue.findUnique({
      where: { id: this.venueId }
    });
    
    this.venueData = venue;
  }

  async scoreNewLead(leadData: LeadData): Promise<LeadScore> {
    const factors = {
      budgetMatch: await this.scoreBudgetMatch(leadData.eventDetails.budget),
      dateAvailability: await this.scoreDateAvailability(leadData.eventDetails.date),
      guestCountFit: this.scoreGuestCountFit(leadData.eventDetails.guestCount),
      engagement: this.scoreEngagement(leadData.inquiryData),
      timing: this.scoreTiming(leadData.eventDetails.date)
    };

    const totalScore = Object.values(factors).reduce((sum, score) => sum + score, 0);
    
    const insights = this.generateInsights(factors, leadData);
    
    return {
      score: totalScore,
      factors,
      recommendation: this.getRecommendation(totalScore),
      insights
    };
  }

  private async scoreBudgetMatch(budget?: string): Promise<number> {
    if (!budget) return 12; // No budget = neutral score
    
    const budgetValue = this.parseBudget(budget);
    const venuePricing = this.venueData.pricing;
    
    // Perfect match: 25 points
    if (budgetValue >= venuePricing.average * 0.9 && budgetValue <= venuePricing.average * 1.5) {
      return 25;
    }
    
    // Good match: 18 points
    if (budgetValue >= venuePricing.minimum && budgetValue <= venuePricing.maximum) {
      return 18;
    }
    
    // Stretch match: 10 points
    if (budgetValue >= venuePricing.minimum * 0.7) {
      return 10;
    }
    
    // Poor match: 5 points
    return 5;
  }

  private async scoreDateAvailability(date: string): Promise<number> {
    const bookings = await prisma.booking.findMany({
      where: {
        venueId: this.venueId,
        status: 'confirmed'
      },
      select: {
        eventDate: true
      }
    });
    
    const requestedDate = new Date(date);
    const isAvailable = !bookings?.some(b => 
      new Date(b.eventDate).toDateString() === requestedDate.toDateString()
    );
    
    if (!isAvailable) return 0; // Date taken = no point in pursuing
    
    // Check if it's a peak date (Saturday in peak season)
    const dayOfWeek = requestedDate.getDay();
    const month = requestedDate.getMonth();
    const isPeakSeason = [4, 5, 8, 9, 10].includes(month); // May, June, Sept, Oct, Nov
    
    if (dayOfWeek === 6 && isPeakSeason) return 25; // Peak Saturday = maximum points
    if (dayOfWeek === 6) return 20; // Off-peak Saturday
    if (dayOfWeek === 5) return 18; // Friday
    if (dayOfWeek === 0) return 15; // Sunday
    
    return 10; // Weekday
  }

  private scoreGuestCountFit(guestCount: number): number {
    const capacity = this.venueData.capacity;
    
    // Perfect fit: 20 points
    if (guestCount >= capacity.ideal_min && guestCount <= capacity.ideal_max) {
      return 20;
    }
    
    // Good fit: 15 points
    if (guestCount >= capacity.minimum && guestCount <= capacity.maximum) {
      return 15;
    }
    
    // Stretch fit: 8 points
    if (guestCount >= capacity.minimum * 0.8 && guestCount <= capacity.maximum * 1.2) {
      return 8;
    }
    
    // Poor fit: 3 points
    return 3;
  }

  private scoreEngagement(inquiryData: any): number {
    let score = 0;
    
    // Message quality (0-8 points)
    const messageLength = inquiryData.message.length;
    if (messageLength > 200) score += 8;
    else if (messageLength > 100) score += 5;
    else score += 2;
    
    // Site engagement (0-7 points)
    if (inquiryData.timeOnSite > 300) score += 4; // 5+ minutes
    if (inquiryData.pagesViewed.length > 5) score += 3;
    
    // Return visitor (0-5 points)
    if (inquiryData.returnVisit) score += 5;
    
    return Math.min(score, 20); // Cap at 20
  }

  private scoreTiming(eventDate: string): number {
    const daysUntilEvent = Math.floor(
      (new Date(eventDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24)
    );
    
    // Ideal timing: 6-12 months out (10 points)
    if (daysUntilEvent >= 180 && daysUntilEvent <= 365) return 10;
    
    // Good timing: 4-6 months or 12-18 months (7 points)
    if ((daysUntilEvent >= 120 && daysUntilEvent < 180) || 
        (daysUntilEvent > 365 && daysUntilEvent <= 540)) return 7;
    
    // Rushed or very far out (4 points)
    return 4;
  }

  private getRecommendation(score: number): 'HOT' | 'WARM' | 'COLD' {
    if (score >= 80) return 'HOT';
    if (score >= 50) return 'WARM';
    return 'COLD';
  }

  private generateInsights(factors: any, leadData: LeadData): string[] {
    const insights: string[] = [];
    
    if (factors.budgetMatch >= 20) {
      insights.push('💰 Budget aligns perfectly with your pricing');
    } else if (factors.budgetMatch <= 10) {
      insights.push('⚠️ Budget may be below your typical range');
    }
    
    if (factors.dateAvailability === 25) {
      insights.push('📅 Peak Saturday date - high revenue opportunity');
    } else if (factors.dateAvailability === 0) {
      insights.push('❌ Requested date is already booked');
    }
    
    if (factors.engagement >= 15) {
      insights.push('🔥 Highly engaged - spent significant time on site');
    }
    
    if (factors.timing === 10) {
      insights.push('⏰ Perfect booking timeline (6-12 months out)');
    }
    
    return insights;
  }

  private parseBudget(budget: string): number {
    // Parse various budget formats: "$10,000", "10k", "10000-15000", etc.
    const numbers = budget.match(/\d+/g);
    if (!numbers) return 0;
    
    const values = numbers.map(n => {
      let value = parseInt(n);
      if (budget.toLowerCase().includes('k')) value *= 1000;
      return value;
    });
    
    // Return average if range, otherwise single value
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  async saveLead(leadData: LeadData, score: LeadScore): Promise<string> {
    const lead = await prisma.lead.create({
      data: {
        venueId: this.venueId,
        contactInfo: leadData.contactInfo,
        eventDetails: leadData.eventDetails,
        score: score.score,
        scoreFactors: score.factors,
        status: score.recommendation,
        insights: score.insights,
        createdAt: new Date()
      }
    });
    
    return lead.id;
  }
}

// Example usage in API route:
/*
export async function POST(request: Request) {
  const leadData = await request.json();
  
  const scorer = new LeadScoringService(venueId);
  await scorer.initialize();
  
  const score = await scorer.scoreNewLead(leadData);
  const leadId = await scorer.saveLead(leadData, score);
  
  // Trigger notifications if HOT lead
  if (score.recommendation === 'HOT') {
    await notifyVenueStaff(leadId, score);
  }
  
  return Response.json({ leadId, score });
}
*/