@echo off
echo Testing Node.js and npm installation...
echo.

REM Add Node.js to PATH for this session
set PATH=%PATH%;C:\Program Files\nodejs

echo Node.js version:
node --version
echo.

echo npm version:
npm --version
echo.

echo Testing npm install in evoque-api...
cd evoque-api
npm install
echo.

echo Testing npm install in evoque-landing...
cd ..\evoque-landing
npm install
echo.

echo Testing npm install in evoque-dashboard...
cd ..\evoque-dashboard
npm install
echo.

echo Testing npm install in evoque-widget...
cd ..\evoque-widget
npm install
echo.

echo All installations complete!
pause
