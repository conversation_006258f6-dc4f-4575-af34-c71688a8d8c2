# Evoque Wedding Platform - Environment Setup Guide

## Overview

This guide provides step-by-step instructions for setting up all required environment variables and external services for the Evoque Wedding Platform.

## Quick Start Checklist

- [ ] **Database**: PostgreSQL with vector extension
- [ ] **AI Services**: OpenAI API key (required)
- [ ] **AI Services**: OpenRouter API key (recommended)
- [ ] **Google Calendar**: OAuth credentials
- [ ] **Communication**: Twilio (SMS) and Resend (Email)
- [ ] **Environment Files**: Created and configured

## Environment Files Setup

### 1. Root Directory (.env.local)

Create `.env.local` in the root directory:

```env
# =================================
# EVOQUE PLATFORM - ROOT CONFIG
# =================================

# API Configuration (REQUIRED)
NEXT_PUBLIC_API_URL=http://localhost:4000
API_BASE_URL=http://localhost:4000

# AI Configuration (REQUIRED)
# OpenRouter API (Recommended - more cost effective)
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-api-key-here
OPENROUTER_MODEL=google/gemini-2.5-flash-lite-preview-06-17

# OpenAI API (Required for embeddings)
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# Notification Services (OPTIONAL - But recommended for production)
# Twilio for SMS
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Resend for Email
RESEND_API_KEY=your_resend_api_key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development

# Analytics (OPTIONAL)
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_HOTJAR_ID=1234567

# Payment Processing (FUTURE)
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...

# File Storage (FUTURE)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_BUCKET=evoque-media-assets
AWS_REGION=us-east-1
```

### 2. API Environment (evoque-api/.env)

Create `.env` in the `evoque-api` directory:

```env
# =================================
# EVOQUE API - BACKEND CONFIG
# =================================

# Database Configuration (REQUIRED)
DB_URL=postgresql://username:password@localhost:5432/evoque_dev

# Legacy PostgreSQL Configuration (if needed)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=evoque_dev
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# AI API Configuration (REQUIRED)
# OpenAI API for embeddings
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# OpenRouter API for chat (recommended)
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-api-key-here
OPENROUTER_MODEL=google/gemini-2.5-flash-lite-preview-06-17
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Alternative: Perplexity API
PERPLEXITY_API_KEY=pplx-your-perplexity-api-key-here

# Google Calendar API (REQUIRED for booking integration)
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI=http://localhost:4000/auth/google/callback

# JWT Configuration (REQUIRED)
JWT_SECRET=your-super-secure-jwt-secret-at-least-32-characters-long
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# Server Configuration
PORT=4000
NODE_ENV=development
LOG_LEVEL=info

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# File Upload
MAX_FILE_SIZE=********

# Communication Services (OPTIONAL)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********
RESEND_API_KEY=your_resend_api_key

# Payment Processing (FUTURE)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

### 3. Landing Page Environment (evoque-landing/.env.local)

Create `.env.local` in the `evoque-landing` directory:

```env
# =================================
# EVOQUE LANDING - FRONTEND CONFIG
# =================================

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:4000
NEXT_PUBLIC_APP_URL=http://localhost:3000

# AI Configuration (for contact forms)
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-api-key-here
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# Communication Services
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********
RESEND_API_KEY=your_resend_api_key

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_HOTJAR_ID=1234567

# Environment
NODE_ENV=development
```

### 4. Dashboard Environment (evoque-dashboard/.env.local)

Create `.env.local` in the `evoque-dashboard` directory:

```env
# =================================
# EVOQUE DASHBOARD - ADMIN CONFIG
# =================================

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:4000
NEXT_PUBLIC_APP_URL=http://localhost:3001

# Authentication
NEXTAUTH_URL=http://localhost:3001
NEXTAUTH_SECRET=your-nextauth-secret-key-here

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Environment
NODE_ENV=development
```

## External Service Setup

### 1. Database Setup (PostgreSQL)

#### Option A: Local PostgreSQL

1. **Install PostgreSQL 14+**
   ```bash
   # Windows (using Chocolatey)
   choco install postgresql
   
   # Or download from: https://www.postgresql.org/download/
   ```

2. **Create Database**
   ```sql
   CREATE DATABASE evoque_dev;
   CREATE USER evoque_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE evoque_dev TO evoque_user;
   ```

3. **Enable Vector Extension**
   ```sql
   -- Connect to evoque_dev database
   CREATE EXTENSION IF NOT EXISTS vector;
   ```

#### Option B: Supabase (Recommended for Development)

1. **Create Account**: https://app.supabase.com
2. **Create New Project**
3. **Get Connection String**: Settings → Database → Connection string
4. **Enable Vector Extension**: SQL Editor → Run `CREATE EXTENSION IF NOT EXISTS vector;`

#### Option C: Render.com PostgreSQL

1. **Create Account**: https://render.com
2. **Create PostgreSQL Database**
3. **Copy connection details**

### 2. AI Services Setup

#### OpenAI API (Required)

1. **Create Account**: https://platform.openai.com
2. **Add Payment Method**: Billing → Payment methods
3. **Create API Key**: API keys → Create new secret key
4. **Copy Key**: Format `sk-proj-...`

**Minimum Credit**: $5 for testing
**Usage**: Embeddings and fallback chat

#### OpenRouter API (Recommended)

1. **Create Account**: https://openrouter.ai
2. **Add Credits**: More cost-effective than direct OpenAI
3. **Create API Key**: Keys → Create key
4. **Copy Key**: Format `sk-or-v1-...`

**Benefits**: 
- Lower costs
- Multiple model access
- Better rate limits

### 3. Google Calendar Integration

1. **Google Cloud Console**: https://console.cloud.google.com
2. **Create Project** or select existing
3. **Enable APIs**:
   - Google Calendar API
   - Google+ API (for user info)
4. **Create Credentials**:
   - Credentials → Create credentials → OAuth 2.0 Client IDs
   - Application type: Web application
   - Authorized redirect URIs: `http://localhost:4000/auth/google/callback`
5. **Copy Client ID and Secret**

### 4. Communication Services

#### Twilio (SMS)

1. **Create Account**: https://www.twilio.com
2. **Get Phone Number**: Console → Phone Numbers
3. **Copy Credentials**:
   - Account SID
   - Auth Token
   - Phone Number

#### Resend (Email)

1. **Create Account**: https://resend.com
2. **Create API Key**: API Keys → Create
3. **Copy API Key**

### 5. Optional Services

#### Stripe (Payment Processing)

1. **Create Account**: https://stripe.com
2. **Get Test Keys**: Developers → API keys
3. **Copy**:
   - Publishable key (`pk_test_...`)
   - Secret key (`sk_test_...`)

#### Google Analytics

1. **Create Account**: https://analytics.google.com
2. **Create Property**
3. **Copy Measurement ID**: `G-XXXXXXXXXX`

#### Hotjar (User Analytics)

1. **Create Account**: https://www.hotjar.com
2. **Create Site**
3. **Copy Site ID**

## Environment Validation

### Validation Script

Create `validate-env.js` in the root directory:

```javascript
// Environment Validation Script
const fs = require('fs');
const path = require('path');

const requiredEnvVars = {
  'evoque-api': [
    'DB_URL',
    'JWT_SECRET',
    'OPENAI_API_KEY',
    'OPENROUTER_API_KEY'
  ],
  'evoque-landing': [
    'NEXT_PUBLIC_API_URL'
  ],
  'evoque-dashboard': [
    'NEXT_PUBLIC_API_URL',
    'NEXTAUTH_SECRET'
  ]
};

function validateEnvironment() {
  console.log('🔍 Validating Environment Configuration...');
  
  let allValid = true;
  
  Object.entries(requiredEnvVars).forEach(([component, vars]) => {
    console.log(`\n📦 ${component}:`);
    
    const envPath = component === 'evoque-api' 
      ? path.join(component, '.env')
      : path.join(component, '.env.local');
    
    if (!fs.existsSync(envPath)) {
      console.log(`  ❌ Environment file missing: ${envPath}`);
      allValid = false;
      return;
    }
    
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    vars.forEach(varName => {
      const regex = new RegExp(`^${varName}=.+`, 'm');
      if (regex.test(envContent)) {
        console.log(`  ✅ ${varName}`);
      } else {
        console.log(`  ❌ ${varName} - Missing or empty`);
        allValid = false;
      }
    });
  });
  
  console.log(`\n${allValid ? '✅' : '❌'} Environment validation ${allValid ? 'passed' : 'failed'}`);
  
  if (!allValid) {
    console.log('\n📚 See ENVIRONMENT_SETUP_GUIDE.md for setup instructions');
  }
  
  return allValid;
}

validateEnvironment();
```

Run with: `node validate-env.js`

## Development Workflow

### 1. Initial Setup

```bash
# 1. Install dependencies
.\SETUP_DEPENDENCIES.ps1

# 2. Set up environment variables (follow this guide)

# 3. Set up database
cd evoque-api
npm run prisma:migrate
npm run prisma:generate
npm run seed

# 4. Validate environment
node validate-env.js
```

### 2. Start Development Servers

```bash
# Terminal 1 - API
cd evoque-api
npm run dev

# Terminal 2 - Landing Page
cd evoque-landing
npm run dev

# Terminal 3 - Dashboard
cd evoque-dashboard
npm run dev

# Terminal 4 - Widget
cd evoque-widget
npm run dev
```

### 3. Access Applications

- **Landing Page**: http://localhost:3000
- **Dashboard**: http://localhost:3001
- **API**: http://localhost:4000
- **Widget**: http://localhost:8080

## Production Environment

### Environment Variables for Production

```env
# Production-specific changes:
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_APP_URL=https://yourdomain.com
CORS_ORIGIN=https://yourdomain.com,https://dashboard.yourdomain.com
GOOGLE_REDIRECT_URI=https://api.yourdomain.com/auth/google/callback

# Use production API keys
# Use production database
# Use strong JWT secrets
```

### Security Checklist

- [ ] **Strong JWT secrets** (32+ characters)
- [ ] **Production API keys** (not test keys)
- [ ] **Secure database credentials**
- [ ] **HTTPS only** in production
- [ ] **Environment variables** not in code
- [ ] **CORS origins** restricted to your domains
- [ ] **Rate limiting** enabled
- [ ] **File upload limits** configured

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check DB_URL format
   - Verify database is running
   - Check firewall settings

2. **AI API Errors**
   - Verify API keys are correct
   - Check account credits/billing
   - Test with curl/Postman

3. **Google OAuth Errors**
   - Check redirect URI matches exactly
   - Verify APIs are enabled
   - Check OAuth consent screen

4. **CORS Errors**
   - Check CORS_ORIGIN setting
   - Verify frontend/backend URLs
   - Check browser developer tools

### Getting Help

- **Documentation**: Check README files in each component
- **Logs**: Check console output for detailed errors
- **Environment**: Run `node validate-env.js`
- **Dependencies**: Run `.\SETUP_DEPENDENCIES.ps1`

---

**Next Steps**: After completing this setup, see `DEPLOYMENT_READINESS_REPORT.md` for deployment instructions.