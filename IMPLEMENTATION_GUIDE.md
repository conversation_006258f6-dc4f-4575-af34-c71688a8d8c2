# Evoque Dashboard Implementation Guide

## Overview

This guide provides step-by-step instructions for implementing the critical missing features identified in the dashboard audit. The implementation addresses two major gaps:

1. **Google Calendar Integration UI** - Complete dashboard interface for Google API setup
2. **Widget Installation System** - Comprehensive widget embedding tools and guides

## 🚀 Quick Start

### Prerequisites

- Node.js 16+ installed
- Access to Google Cloud Console
- Basic understanding of React/Next.js

### Installation Steps

1. **Install Dependencies**
   ```bash
   cd evoque-dashboard
   npm install
   ```

2. **Environment Setup**
   ```bash
   # Add to .env.local
   NEXT_PUBLIC_API_URL=http://localhost:4000
   NEXT_PUBLIC_WIDGET_URL=https://cdn.evoque.digital
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

## 📋 Implementation Checklist

### ✅ Completed Features

- [x] Google Calendar Integration Component (`GoogleCalendarIntegration.tsx`)
- [x] Widget Embed Code Generator (`WidgetEmbedGenerator.tsx`)
- [x] Widget Testing Page (`widget-test.tsx`)
- [x] Settings Page Integration
- [x] Platform-specific Installation Guides
- [x] OAuth Flow UI
- [x] Widget Preview System

### 🔄 Next Steps Required

#### Backend API Endpoints

1. **Calendar Integration Endpoints**
   ```typescript
   // Required GraphQL mutations/queries
   - checkCalendarStatus(venueId: ID!)
   - generateCalendarOAuthUrl(venueId: ID!)
   - disconnectCalendar(venueId: ID!)
   ```

2. **Widget Configuration Endpoints**
   ```typescript
   // Update existing venue settings schema
   type VenueSettings {
     chatWidget: {
       title: String
       welcomeMessage: String
       primaryColor: String
       position: String
       autoOpen: Boolean
     }
   }
   ```

#### Environment Variables

```bash
# Google Calendar Integration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=http://localhost:3000/api/calendar/oauth-callback

# Widget Configuration
NEXT_PUBLIC_WIDGET_URL=https://cdn.evoque.digital
NEXT_PUBLIC_API_URL=http://localhost:4000
```

## 🎯 Feature Implementation Details

### Google Calendar Integration

#### Component Location
```
evoque-dashboard/src/components/settings/GoogleCalendarIntegration.tsx
```

#### Key Features
- **Connection Status Display** - Shows current calendar connection state
- **Step-by-Step Setup Guide** - Guides users through Google Cloud Console setup
- **OAuth URL Generation** - Creates secure authentication links
- **Disconnect Functionality** - Allows users to remove calendar integration
- **Error Handling** - Comprehensive error messages and troubleshooting

#### User Flow
1. User navigates to Settings → Google Calendar
2. System checks current connection status
3. If not connected, displays setup guide
4. User follows Google Cloud Console setup steps
5. User generates OAuth URL and completes authentication
6. System confirms successful connection

### Widget Installation System

#### Component Location
```
evoque-dashboard/src/components/settings/WidgetEmbedGenerator.tsx
```

#### Key Features
- **Platform Selection** - WordPress, Wix, Shopify, Squarespace, Custom HTML
- **Dynamic Embed Code Generation** - Creates customized installation code
- **Platform-Specific Instructions** - Detailed steps for each platform
- **Live Widget Preview** - Shows how widget will appear on site
- **Installation Testing** - Links to widget test page
- **Copy-to-Clipboard** - Easy code copying functionality

#### Supported Platforms

1. **WordPress**
   - Theme Editor method
   - Plugin-based installation
   - WordPress.com considerations

2. **Wix**
   - Custom Code feature
   - Premium plan requirements
   - Placement options

3. **Shopify**
   - Theme liquid file editing
   - Store-wide implementation
   - Testing procedures

4. **Squarespace**
   - Code Injection feature
   - Business plan requirements
   - Footer placement

5. **Custom HTML**
   - Direct HTML integration
   - Static site generators
   - Server upload instructions

### Widget Testing System

#### Page Location
```
evoque-dashboard/src/pages/widget-test.tsx
```

#### Testing Features
- **Script Loading Verification** - Checks if widget script loads
- **Venue ID Validation** - Ensures correct venue configuration
- **API Connectivity Test** - Verifies backend connection
- **Widget Initialization Check** - Confirms widget starts properly
- **Chat Functionality Test** - Validates user interface
- **Troubleshooting Guide** - Provides solutions for common issues

## 🔧 Technical Architecture

### Component Structure
```
evoque-dashboard/src/
├── components/
│   └── settings/
│       ├── GoogleCalendarIntegration.tsx
│       └── WidgetEmbedGenerator.tsx
├── pages/
│   ├── settings/
│   │   └── index.tsx (updated)
│   └── widget-test.tsx
└── context/
    └── AuthContext.tsx
```

### State Management
- **Apollo Client** - GraphQL state management
- **React Hooks** - Local component state
- **Context API** - Authentication state

### Styling
- **Tailwind CSS** - Utility-first styling
- **Heroicons** - Consistent iconography
- **Responsive Design** - Mobile-first approach

## 🎨 User Experience Design

### Design Principles
1. **Simplicity First** - Clear, uncluttered interfaces
2. **Progressive Disclosure** - Show information when needed
3. **Visual Feedback** - Clear status indicators and loading states
4. **Error Prevention** - Validation and helpful guidance
5. **Accessibility** - WCAG 2.1 AA compliance

### Key UX Improvements
- **Step-by-Step Wizards** - Guided setup processes
- **Visual Previews** - See changes before implementation
- **Copy-Paste Functionality** - Reduce manual typing errors
- **Platform-Specific Guidance** - Tailored instructions
- **Testing Tools** - Verify implementation success

## 🧪 Testing Strategy

### Manual Testing Checklist

#### Google Calendar Integration
- [ ] Connection status displays correctly
- [ ] Setup guide is clear and actionable
- [ ] OAuth flow completes successfully
- [ ] Disconnect functionality works
- [ ] Error states are handled gracefully

#### Widget Installation
- [ ] Embed code generates correctly
- [ ] Platform instructions are accurate
- [ ] Preview shows correct widget appearance
- [ ] Copy functionality works on all browsers
- [ ] Test page validates installation

### Automated Testing
```bash
# Run component tests
npm run test

# Run E2E tests
npm run test:e2e

# Run accessibility tests
npm run test:a11y
```

## 🚀 Deployment Guide

### Pre-Deployment Checklist
1. **Environment Variables** - All required vars set
2. **API Endpoints** - Backend endpoints implemented
3. **Google Cloud Setup** - OAuth credentials configured
4. **Widget CDN** - Widget files deployed to CDN
5. **Testing** - All features tested in staging

### Deployment Steps
1. **Build Application**
   ```bash
   npm run build
   ```

2. **Deploy to Production**
   ```bash
   npm run deploy
   ```

3. **Verify Deployment**
   - Test Google Calendar integration
   - Verify widget embed generation
   - Check widget test page functionality

## 📚 User Documentation

### For End Users
1. **Google Calendar Setup Guide** - Step-by-step instructions
2. **Widget Installation Guide** - Platform-specific tutorials
3. **Troubleshooting Guide** - Common issues and solutions
4. **Video Tutorials** - Visual learning resources

### For Developers
1. **API Documentation** - GraphQL schema and endpoints
2. **Component Documentation** - Props and usage examples
3. **Customization Guide** - Theming and configuration
4. **Integration Examples** - Sample implementations

## 🔒 Security Considerations

### Google Calendar Integration
- **OAuth 2.0 Flow** - Secure authentication
- **Token Storage** - Encrypted token management
- **Scope Limitation** - Minimal required permissions
- **Regular Audits** - Periodic security reviews

### Widget Installation
- **XSS Prevention** - Input sanitization
- **CSP Headers** - Content Security Policy
- **HTTPS Only** - Secure transmission
- **Rate Limiting** - API abuse prevention

## 📈 Performance Optimization

### Frontend Optimization
- **Code Splitting** - Lazy load components
- **Image Optimization** - WebP format and compression
- **Bundle Analysis** - Regular size monitoring
- **Caching Strategy** - Efficient cache headers

### Backend Optimization
- **GraphQL Optimization** - Efficient queries
- **Database Indexing** - Fast data retrieval
- **CDN Usage** - Global content delivery
- **Monitoring** - Performance tracking

## 🐛 Troubleshooting

### Common Issues

#### Google Calendar Not Connecting
1. Check Google Cloud Console configuration
2. Verify redirect URI matches exactly
3. Ensure Calendar API is enabled
4. Check OAuth consent screen setup

#### Widget Not Appearing
1. Verify embed code placement
2. Check for JavaScript errors
3. Confirm venue ID is correct
4. Test API connectivity

#### Installation Instructions Unclear
1. Review platform-specific guides
2. Check for updated platform requirements
3. Verify screenshots are current
4. Test instructions on actual platforms

## 📞 Support Resources

### Documentation Links
- [Google Calendar API Documentation](https://developers.google.com/calendar)
- [OAuth 2.0 Setup Guide](https://developers.google.com/identity/protocols/oauth2)
- [Widget Integration Examples](./WIDGET_EXAMPLES.md)
- [API Reference](./API_REFERENCE.md)

### Contact Information
- **Technical Support**: <EMAIL>
- **Documentation Issues**: <EMAIL>
- **Feature Requests**: <EMAIL>

## 🎯 Success Metrics

### Key Performance Indicators
- **Setup Completion Rate** - % of users who complete setup
- **Time to First Success** - Average time to working integration
- **Support Ticket Reduction** - Decrease in setup-related tickets
- **User Satisfaction** - Feedback scores and reviews

### Monitoring Dashboard
- Real-time setup success rates
- Common failure points
- User journey analytics
- Performance metrics

---

## 🎉 Conclusion

This implementation provides a comprehensive solution to the critical gaps identified in the dashboard audit. The new features ensure that non-technical users can successfully:

1. **Set up Google Calendar integration** with clear, step-by-step guidance
2. **Install chat widgets** on any supported platform with confidence
3. **Test their implementation** to ensure everything works correctly
4. **Get help when needed** through comprehensive troubleshooting guides

The implementation follows best practices for user experience, security, and performance, ensuring a professional and reliable platform for all users.