import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import {
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  ClipboardDocumentIcon,
  CheckIcon,
} from '@heroicons/react/24/outline';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'warning' | 'pending';
  message: string;
  details?: string;
}

const WidgetTest: React.FC = () => {
  const router = useRouter();
  const { venueId } = router.query;
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [copied, setCopied] = useState(false);

  const initialTests: TestResult[] = [
    {
      name: 'Widget Script Loading',
      status: 'pending',
      message: 'Checking if widget script loads correctly...',
    },
    {
      name: 'Venue ID Validation',
      status: 'pending',
      message: 'Validating venue ID parameter...',
    },
    {
      name: 'API Connectivity',
      status: 'pending',
      message: 'Testing connection to Evoque API...',
    },
    {
      name: 'Widget Initialization',
      status: 'pending',
      message: 'Checking widget initialization...',
    },
    {
      name: 'Chat Functionality',
      status: 'pending',
      message: 'Testing chat interface...',
    },
  ];

  useEffect(() => {
    if (venueId) {
      setTestResults(initialTests);
      runTests();
    }
  }, [venueId]);

  const runTests = async () => {
    setIsRunning(true);
    const results = [...initialTests];

    // Test 1: Widget Script Loading
    try {
      const scriptExists = document.querySelector('script[src*="evoque"]');
      if (scriptExists) {
        results[0] = {
          name: 'Widget Script Loading',
          status: 'pass',
          message: 'Widget script found and loaded successfully',
          details: 'The Evoque widget script is properly included in the page',
        };
      } else {
        results[0] = {
          name: 'Widget Script Loading',
          status: 'fail',
          message: 'Widget script not found',
          details: 'Make sure the embed code is properly installed before the closing </body> tag',
        };
      }
    } catch (error) {
      results[0] = {
        name: 'Widget Script Loading',
        status: 'fail',
        message: 'Error checking widget script',
        details: 'Unable to verify script loading',
      };
    }
    setTestResults([...results]);
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test 2: Venue ID Validation
    if (venueId && typeof venueId === 'string' && venueId.length > 0) {
      results[1] = {
        name: 'Venue ID Validation',
        status: 'pass',
        message: 'Venue ID is valid',
        details: `Using venue ID: ${venueId}`,
      };
    } else {
      results[1] = {
        name: 'Venue ID Validation',
        status: 'fail',
        message: 'Invalid or missing venue ID',
        details: 'Venue ID is required for the widget to function properly',
      };
    }
    setTestResults([...results]);
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test 3: API Connectivity
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
      const response = await fetch(`${apiUrl}/health`, { method: 'GET' });
      if (response.ok) {
        results[2] = {
          name: 'API Connectivity',
          status: 'pass',
          message: 'Successfully connected to Evoque API',
          details: `API endpoint: ${apiUrl}`,
        };
      } else {
        results[2] = {
          name: 'API Connectivity',
          status: 'warning',
          message: 'API connection issues detected',
          details: 'The API is reachable but may be experiencing issues',
        };
      }
    } catch (error) {
      results[2] = {
        name: 'API Connectivity',
        status: 'fail',
        message: 'Cannot connect to Evoque API',
        details: 'Check your internet connection and API configuration',
      };
    }
    setTestResults([...results]);
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test 4: Widget Initialization
    try {
      // Check if EvoqueWidget is available globally
      const widgetExists = (window as any).EvoqueWidget;
      if (widgetExists) {
        results[3] = {
          name: 'Widget Initialization',
          status: 'pass',
          message: 'Widget initialized successfully',
          details: 'EvoqueWidget class is available and ready',
        };
      } else {
        results[3] = {
          name: 'Widget Initialization',
          status: 'warning',
          message: 'Widget not yet initialized',
          details: 'Widget may still be loading or there may be a configuration issue',
        };
      }
    } catch (error) {
      results[3] = {
        name: 'Widget Initialization',
        status: 'fail',
        message: 'Widget initialization failed',
        details: 'Error during widget initialization process',
      };
    }
    setTestResults([...results]);
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test 5: Chat Functionality
    try {
      const chatButton = document.querySelector('[data-evoque-widget]');
      if (chatButton) {
        results[4] = {
          name: 'Chat Functionality',
          status: 'pass',
          message: 'Chat interface is ready',
          details: 'Widget button is visible and functional',
        };
      } else {
        results[4] = {
          name: 'Chat Functionality',
          status: 'warning',
          message: 'Chat interface not detected',
          details: 'Widget may be hidden or not fully loaded yet',
        };
      }
    } catch (error) {
      results[4] = {
        name: 'Chat Functionality',
        status: 'fail',
        message: 'Chat functionality test failed',
        details: 'Unable to verify chat interface',
      };
    }
    setTestResults([...results]);
    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'fail':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'pending':
        return <ArrowPathIcon className="h-5 w-5 text-gray-400 animate-spin" />;
      default:
        return <ArrowPathIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return 'border-green-200 bg-green-50';
      case 'fail':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'pending':
        return 'border-gray-200 bg-gray-50';
      default:
        return 'border-gray-200 bg-white';
    }
  };

  const copyTestResults = async () => {
    const resultsText = testResults
      .map(test => `${test.name}: ${test.status.toUpperCase()} - ${test.message}`)
      .join('\n');
    
    try {
      await navigator.clipboard.writeText(resultsText);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy results:', err);
    }
  };

  const overallStatus = testResults.length > 0 ? (
    testResults.every(test => test.status === 'pass') ? 'pass' :
    testResults.some(test => test.status === 'fail') ? 'fail' : 'warning'
  ) : 'pending';

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-5 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Widget Installation Test</h1>
                <p className="mt-1 text-sm text-gray-500">
                  Testing your Evoque chat widget installation
                  {venueId && <span className="ml-2 font-medium">Venue ID: {venueId}</span>}
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={copyTestResults}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  {copied ? (
                    <>
                      <CheckIcon className="h-4 w-4 mr-2" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <ClipboardDocumentIcon className="h-4 w-4 mr-2" />
                      Copy Results
                    </>
                  )}
                </button>
                <button
                  onClick={runTests}
                  disabled={isRunning}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                >
                  {isRunning ? (
                    <>
                      <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                      Running Tests...
                    </>
                  ) : (
                    'Run Tests Again'
                  )}
                </button>
              </div>
            </div>
          </div>

          <div className="px-6 py-5">
            {/* Overall Status */}
            <div className={`mb-6 p-4 rounded-lg border ${getStatusColor(overallStatus)}`}>
              <div className="flex items-center">
                {getStatusIcon(overallStatus)}
                <div className="ml-3">
                  <h3 className="text-sm font-medium">
                    {overallStatus === 'pass' && 'All Tests Passed!'}
                    {overallStatus === 'fail' && 'Some Tests Failed'}
                    {overallStatus === 'warning' && 'Tests Completed with Warnings'}
                    {overallStatus === 'pending' && 'Running Tests...'}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {overallStatus === 'pass' && 'Your widget is properly installed and functioning correctly.'}
                    {overallStatus === 'fail' && 'Please review the failed tests and fix any issues.'}
                    {overallStatus === 'warning' && 'Your widget is working but there may be some issues to address.'}
                    {overallStatus === 'pending' && 'Please wait while we test your widget installation.'}
                  </p>
                </div>
              </div>
            </div>

            {/* Test Results */}
            <div className="space-y-4">
              {testResults.map((test, index) => (
                <div key={index} className={`border rounded-lg p-4 ${getStatusColor(test.status)}`}>
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      {getStatusIcon(test.status)}
                    </div>
                    <div className="ml-3 flex-1">
                      <h4 className="text-sm font-medium text-gray-900">{test.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">{test.message}</p>
                      {test.details && (
                        <p className="text-xs text-gray-500 mt-2">{test.details}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Troubleshooting Guide */}
            <div className="mt-8 bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Troubleshooting Guide</h3>
              <div className="space-y-4 text-sm">
                <div>
                  <h4 className="font-medium text-gray-700">Widget Script Not Loading</h4>
                  <ul className="mt-1 text-gray-600 list-disc list-inside space-y-1">
                    <li>Ensure the embed code is placed before the closing &lt;/body&gt; tag</li>
                    <li>Check for JavaScript errors in the browser console</li>
                    <li>Verify the script URL is accessible</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-700">API Connection Issues</h4>
                  <ul className="mt-1 text-gray-600 list-disc list-inside space-y-1">
                    <li>Check your internet connection</li>
                    <li>Verify the API endpoint is correct</li>
                    <li>Ensure there are no firewall restrictions</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-700">Widget Not Appearing</h4>
                  <ul className="mt-1 text-gray-600 list-disc list-inside space-y-1">
                    <li>Check if the widget is hidden by CSS styles</li>
                    <li>Verify the venue ID is correct</li>
                    <li>Ensure the widget position settings are appropriate</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Support Links */}
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500">
                Need more help? 
                <a href="#" className="text-primary-600 hover:text-primary-500 ml-1">
                  Contact Support
                </a>
                {' | '}
                <a href="#" className="text-primary-600 hover:text-primary-500 ml-1">
                  View Documentation
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WidgetTest;