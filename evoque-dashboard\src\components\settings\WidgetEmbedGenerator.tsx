import React, { useState, useEffect } from 'react';
import { useMutation, useQuery, gql } from '@apollo/client';
import {
  CodeBracketIcon,
  ClipboardDocumentIcon,
  CheckIcon,
  EyeIcon,
  Cog6ToothIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  GlobeAltIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/context/AuthContext';

// Platform-specific installation guides
const PLATFORM_GUIDES = {
  wordpress: {
    name: 'WordPress',
    icon: '🔧',
    steps: [
      'Log in to your WordPress admin dashboard',
      'Go to Appearance → Theme Editor or use a plugin like "Insert Headers and Footers"',
      'Paste the embed code before the closing </body> tag',
      'Save changes and test your website'
    ],
    notes: 'For WordPress.com sites, you may need a Business plan to add custom code.'
  },
  wix: {
    name: 'Wix',
    icon: '🎨',
    steps: [
      'Open your Wix site editor',
      'Click on Settings in the top menu',
      'Go to Advanced → Custom Code',
      'Click "Add Custom Code"',
      'Paste the embed code and set it to load on "All Pages"',
      'Choose "Body - end" as the placement',
      'Save and publish your site'
    ],
    notes: 'Make sure you have a premium Wix plan to use custom code features.'
  },
  shopify: {
    name: 'Shopify',
    icon: '🛒',
    steps: [
      'Go to your Shopify admin panel',
      'Navigate to Online Store → Themes',
      'Click "Actions" → "Edit code" on your active theme',
      'Find and open the "theme.liquid" file',
      'Paste the embed code before the closing </body> tag',
      'Save the file'
    ],
    notes: 'Changes will apply to all pages. Test thoroughly before going live.'
  },
  squarespace: {
    name: 'Squarespace',
    icon: '⬜',
    steps: [
      'Log in to your Squarespace account',
      'Go to Settings → Advanced → Code Injection',
      'Paste the embed code in the "Footer" section',
      'Save changes',
      'The widget will appear on all pages of your site'
    ],
    notes: 'Code injection is available on Business plans and higher.'
  },
  html: {
    name: 'Custom HTML',
    icon: '💻',
    steps: [
      'Open your website\'s HTML file in a text editor',
      'Locate the closing </body> tag',
      'Paste the embed code just before the </body> tag',
      'Save the file and upload it to your web server'
    ],
    notes: 'This method works for any custom HTML website or static site generator.'
  }
};

interface WidgetSettings {
  title: string;
  welcomeMessage: string;
  primaryColor: string;
  position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  autoOpen: boolean;
}

interface WidgetEmbedGeneratorProps {
  venueId: string;
  settings: WidgetSettings;
}

const WidgetEmbedGenerator: React.FC<WidgetEmbedGeneratorProps> = ({ venueId, settings }) => {
  const [selectedPlatform, setSelectedPlatform] = useState<keyof typeof PLATFORM_GUIDES>('html');
  const [copiedCode, setCopiedCode] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'mobile'>('desktop');

  // Generate the embed code based on current settings
  const generateEmbedCode = () => {
    const baseUrl = process.env.NEXT_PUBLIC_WIDGET_URL || 'https://cdn.evoque.digital';
    
    return `<!-- Evoque Chat Widget -->
<script>
  (function() {
    var script = document.createElement('script');
    script.src = '${baseUrl}/widget/v2/loader.js';
    script.async = true;
    script.onload = function() {
      new EvoqueWidget({
        venueId: '${venueId}',
        primaryColor: '${settings.primaryColor}',
        position: '${settings.position}',
        welcomeMessage: '${settings.welcomeMessage.replace(/'/g, "\\'").replace(/"/g, '\\"')}',
        autoOpen: ${settings.autoOpen},
        title: '${settings.title.replace(/'/g, "\\'").replace(/"/g, '\\"')}'
      });
    };
    document.head.appendChild(script);
  })();
</script>
<!-- End Evoque Chat Widget -->`;
  };

  const embedCode = generateEmbedCode();

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(embedCode);
      setCopiedCode(true);
      setTimeout(() => setCopiedCode(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const testInstallation = () => {
    // This would typically make an API call to test if the widget is properly installed
    window.open(`${window.location.origin}/widget-test?venueId=${venueId}`, '_blank');
  };

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-5 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CodeBracketIcon className="h-6 w-6 text-primary-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">Widget Installation</h3>
              <p className="text-sm text-gray-500">
                Get your embed code and install the chat widget on your website
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowPreview(!showPreview)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <EyeIcon className="h-4 w-4 mr-2" />
              {showPreview ? 'Hide Preview' : 'Show Preview'}
            </button>
          </div>
        </div>
      </div>

      <div className="px-6 py-5">
        <div className="space-y-6">
          {/* Platform Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Choose Your Website Platform
            </label>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
              {Object.entries(PLATFORM_GUIDES).map(([key, platform]) => (
                <button
                  key={key}
                  onClick={() => setSelectedPlatform(key as keyof typeof PLATFORM_GUIDES)}
                  className={`p-3 border rounded-lg text-center transition-colors ${
                    selectedPlatform === key
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 hover:border-gray-300 text-gray-700'
                  }`}
                >
                  <div className="text-2xl mb-1">{platform.icon}</div>
                  <div className="text-xs font-medium">{platform.name}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Embed Code */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <label className="block text-sm font-medium text-gray-700">
                Embed Code
              </label>
              <button
                onClick={copyToClipboard}
                className={`inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded ${
                  copiedCode
                    ? 'text-green-700 bg-green-100'
                    : 'text-primary-700 bg-primary-100 hover:bg-primary-200'
                }`}
              >
                {copiedCode ? (
                  <>
                    <CheckIcon className="h-3 w-3 mr-1" />
                    Copied!
                  </>
                ) : (
                  <>
                    <ClipboardDocumentIcon className="h-3 w-3 mr-1" />
                    Copy Code
                  </>
                )}
              </button>
            </div>
            <div className="relative">
              <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-xs font-mono">
                <code>{embedCode}</code>
              </pre>
            </div>
          </div>

          {/* Installation Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-3">
              {PLATFORM_GUIDES[selectedPlatform].icon} Installation Instructions for {PLATFORM_GUIDES[selectedPlatform].name}
            </h4>
            <ol className="list-decimal list-inside space-y-2 text-sm text-blue-800">
              {PLATFORM_GUIDES[selectedPlatform].steps.map((step, index) => (
                <li key={index}>{step}</li>
              ))}
            </ol>
            {PLATFORM_GUIDES[selectedPlatform].notes && (
              <div className="mt-3 p-3 bg-blue-100 rounded border border-blue-200">
                <p className="text-xs text-blue-700">
                  <strong>Note:</strong> {PLATFORM_GUIDES[selectedPlatform].notes}
                </p>
              </div>
            )}
          </div>

          {/* Widget Preview */}
          {showPreview && (
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium text-gray-900">Widget Preview</h4>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setPreviewDevice('desktop')}
                    className={`p-2 rounded ${
                      previewDevice === 'desktop'
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <ComputerDesktopIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setPreviewDevice('mobile')}
                    className={`p-2 rounded ${
                      previewDevice === 'mobile'
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <DevicePhoneMobileIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
              
              <div className={`bg-gray-100 rounded-lg overflow-hidden ${
                previewDevice === 'mobile' ? 'max-w-sm mx-auto' : 'w-full'
              }`}>
                <div className="bg-white border-b border-gray-200 px-4 py-2 flex items-center space-x-2">
                  <div className="flex space-x-1">
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  </div>
                  <div className="flex-1 bg-gray-100 rounded px-3 py-1 text-xs text-gray-600">
                    yourwebsite.com
                  </div>
                </div>
                
                <div className={`relative bg-gradient-to-br from-blue-50 to-indigo-100 ${
                  previewDevice === 'mobile' ? 'h-96' : 'h-64'
                }`}>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <GlobeAltIcon className="h-12 w-12 mx-auto mb-2" />
                      <p className="text-sm">Your Website Content</p>
                    </div>
                  </div>
                  
                  {/* Widget Preview */}
                  <div className={`absolute ${
                    settings.position.includes('bottom') ? 'bottom-4' : 'top-4'
                  } ${
                    settings.position.includes('right') ? 'right-4' : 'left-4'
                  }`}>
                    <div 
                      className="w-14 h-14 rounded-full shadow-lg flex items-center justify-center cursor-pointer transform hover:scale-105 transition-transform"
                      style={{ backgroundColor: settings.primaryColor }}
                    >
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.471L3 21l2.471-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-4 text-center">
                <button
                  onClick={testInstallation}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <GlobeAltIcon className="h-4 w-4 mr-2" />
                  Test Widget Functionality
                </button>
              </div>
            </div>
          )}

          {/* Additional Resources */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">Need Help?</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h5 className="font-medium text-gray-700 mb-2">Common Issues</h5>
                <ul className="space-y-1 text-gray-600">
                  <li>• Widget not appearing: Check if code is before &lt;/body&gt; tag</li>
                  <li>• Multiple widgets: Remove duplicate embed codes</li>
                  <li>• Styling conflicts: Contact support for custom CSS</li>
                </ul>
              </div>
              <div>
                <h5 className="font-medium text-gray-700 mb-2">Support Resources</h5>
                <ul className="space-y-1 text-gray-600">
                  <li>• <a href="#" className="text-primary-600 hover:text-primary-700">Installation Video Tutorial</a></li>
                  <li>• <a href="#" className="text-primary-600 hover:text-primary-700">Troubleshooting Guide</a></li>
                  <li>• <a href="#" className="text-primary-600 hover:text-primary-700">Contact Support</a></li>
                </ul>
              </div>
            </div>
          </div>

          {/* Widget Settings Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">Current Widget Settings</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Title:</span>
                <p className="font-medium">{settings.title}</p>
              </div>
              <div>
                <span className="text-gray-600">Position:</span>
                <p className="font-medium capitalize">{settings.position.replace('-', ' ')}</p>
              </div>
              <div>
                <span className="text-gray-600">Color:</span>
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-4 h-4 rounded border border-gray-300"
                    style={{ backgroundColor: settings.primaryColor }}
                  ></div>
                  <span className="font-medium">{settings.primaryColor}</span>
                </div>
              </div>
              <div>
                <span className="text-gray-600">Auto-open:</span>
                <p className="font-medium">{settings.autoOpen ? 'Yes' : 'No'}</p>
              </div>
            </div>
            <div className="mt-3">
              <span className="text-gray-600">Welcome Message:</span>
              <p className="font-medium text-sm mt-1">"{settings.welcomeMessage}"</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WidgetEmbedGenerator;