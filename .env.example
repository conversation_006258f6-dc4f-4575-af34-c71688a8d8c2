# Evoque Wedding Platform Environment Variables
# Copy this to .env.local and fill in your values

# API Configuration (REQUIRED)
NEXT_PUBLIC_API_URL=http://localhost:4000
API_BASE_URL=http://localhost:4000

# AI Configuration (REQUIRED)
# OpenRouter API (Recommended)
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-api-key
OPENROUTER_MODEL=google/gemini-2.5-flash-lite-preview-06-17

# Notification Services (OPTIONAL - But recommended for production)
# Twilio for SMS
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Resend for Email
RESEND_API_KEY=your_resend_api_key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development

# Analytics (OPTIONAL)
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_HOTJAR_ID=1234567

# Payment Processing (FUTURE)
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...

# File Storage (FUTURE)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_BUCKET=evoque-media-assets
AWS_REGION=us-east-1