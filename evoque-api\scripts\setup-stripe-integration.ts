import { PrismaClient } from '@prisma/client';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const prisma = new PrismaClient();

/**
 * Complete setup script for Stripe integration
 */
async function setupStripeIntegration() {
  console.log('🚀 Setting up Stripe Integration for Evoque Platform...\n');

  try {
    // Step 1: Database Setup
    console.log('📊 Step 1: Database Setup');
    await setupDatabase();

    // Step 2: Seed Subscription Plans
    console.log('💳 Step 2: Seeding Subscription Plans');
    await seedSubscriptionPlans();

    // Step 3: Create Demo Venue
    console.log('🏰 Step 3: Creating Demo Venue');
    await createDemoVenue();

    // Step 4: Setup Admin User
    console.log('👑 Step 4: Setting up Admin User');
    await setupAdminUser();

    // Step 5: Validate Integration
    console.log('🧪 Step 5: Validating Integration');
    await validateIntegration();

    // Step 6: Display Configuration Instructions
    console.log('📋 Step 6: Configuration Instructions');
    displayConfigurationInstructions();

    console.log('🎉 Stripe integration setup completed successfully!\n');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function setupDatabase(): Promise<void> {
  try {
    console.log('   🔌 Testing database connection...');
    await prisma.$connect();
    console.log('   ✅ Database connection successful');

    console.log('   📋 Checking existing tables...');
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('subscription_plans', 'subscriptions', 'payments')
    ` as any[];

    const hasPaymentTables = tables.length === 3;
    
    if (!hasPaymentTables) {
      console.log('   🔧 Payment tables not found, creating them...');
      // Since migration might be slow, we'll use db push for faster setup
      await execAsync('npx prisma db push', { cwd: process.cwd() });
      console.log('   ✅ Database schema updated');
    } else {
      console.log('   ✅ Payment tables already exist');
    }

  } catch (error) {
    console.error('   ❌ Database setup failed:', error);
    throw error;
  }
}

async function seedSubscriptionPlans(): Promise<void> {
  try {
    const existingPlans = await prisma.subscriptionPlan.count();
    
    if (existingPlans === 0) {
      console.log('   📝 Creating subscription plans...');
      const { seedSubscriptionPlans } = await import('../prisma/seed-subscription-plans');
      await seedSubscriptionPlans();
    } else {
      console.log('   ✅ Subscription plans already exist');
    }

    const plans = await prisma.subscriptionPlan.findMany();
    console.log(`   📊 Total plans: ${plans.length}`);
    
  } catch (error) {
    console.error('   ❌ Subscription plans seeding failed:', error);
    throw error;
  }
}

async function createDemoVenue(): Promise<void> {
  try {
    const existingDemo = await prisma.venue.findUnique({
      where: { slug: 'demo-venue' }
    });

    if (!existingDemo) {
      console.log('   🏗️  Creating demo venue...');
      const { createDemoVenue } = await import('./create-demo-venue');
      await createDemoVenue();
    } else {
      console.log('   ✅ Demo venue already exists');
    }

    const demoVenue = await prisma.venue.findUnique({
      where: { slug: 'demo-venue' },
      include: {
        subscription: true,
        venueUsers: true,
        weddingPackages: true,
        inquiries: true
      }
    });

    console.log(`   🏰 Demo venue: ${demoVenue?.name}`);
    console.log(`   📦 Packages: ${demoVenue?.weddingPackages.length}`);
    console.log(`   📧 Inquiries: ${demoVenue?.inquiries.length}`);
    
  } catch (error) {
    console.error('   ❌ Demo venue creation failed:', error);
    throw error;
  }
}

async function setupAdminUser(): Promise<void> {
  try {
    // Check if admin role exists
    let adminRole = await prisma.role.findFirst({
      where: { name: 'admin' }
    });

    if (!adminRole) {
      console.log('   👑 Creating admin role...');
      adminRole = await prisma.role.create({
        data: {
          name: 'admin',
          description: 'System administrator with full access',
          isSystem: true
        }
      });
    }

    // Check if admin user exists
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      console.log('   👤 Creating admin user...');
      const bcrypt = await import('bcrypt');
      
      const newAdminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          firstName: 'Admin',
          lastName: 'User',
          status: 'active',
          emailVerified: true,
          passwordHash: await bcrypt.hash('admin123', 10)
        }
      });

      // Assign admin role
      await prisma.userRole.create({
        data: {
          userId: newAdminUser.id,
          roleId: adminRole.id
        }
      });

      console.log('   ✅ Admin user created');
    } else {
      console.log('   ✅ Admin user already exists');
    }

  } catch (error) {
    console.error('   ❌ Admin user setup failed:', error);
    throw error;
  }
}

async function validateIntegration(): Promise<void> {
  try {
    console.log('   🧪 Running validation tests...');
    
    const { StripeIntegrationTester } = await import('./test-stripe-integration');
    const tester = new StripeIntegrationTester();
    await tester.runAllTests();
    
  } catch (error) {
    console.error('   ❌ Validation failed:', error);
    throw error;
  }
}

function displayConfigurationInstructions(): void {
  console.log(`
🔧 CONFIGURATION INSTRUCTIONS
=============================

1. STRIPE API KEYS SETUP:
   -------------------------
   Update your .env file with actual Stripe keys:
   
   STRIPE_SECRET_KEY=sk_test_your_test_secret_key
   STRIPE_PUBLISHABLE_KEY=pk_test_your_test_publishable_key
   STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
   STRIPE_CONNECT_CLIENT_ID=ca_your_connect_client_id

2. STRIPE WEBHOOK CONFIGURATION:
   ------------------------------
   Set up webhook endpoint in Stripe Dashboard:
   URL: https://your-domain.com/api/stripe/webhook
   Events to listen for:
   - payment_intent.succeeded
   - payment_intent.payment_failed
   - invoice.payment_succeeded
   - invoice.payment_failed
   - customer.subscription.created
   - customer.subscription.updated
   - customer.subscription.deleted
   - charge.dispute.created

3. STRIPE PRODUCTS AND PRICES:
   ----------------------------
   Create products in Stripe Dashboard for each subscription plan:
   - Starter Plan: $199/month
   - Professional Plan: $399/month
   - Enterprise Plan: $799/month
   
   Update subscription_plans table with Stripe price IDs.

4. DEMO ACCOUNT ACCESS:
   --------------------
   Demo Venue: <EMAIL> / demo123
   Admin Panel: <EMAIL> / admin123
   
   Widget URL: /widget/demo-venue

5. TESTING CHECKLIST:
   ------------------
   □ Test payment intent creation
   □ Test subscription creation
   □ Test webhook handling
   □ Test admin user management
   □ Test demo venue isolation
   □ Test subscription cleanup
   □ Test refund processing

6. PRODUCTION DEPLOYMENT:
   ----------------------
   □ Replace test keys with live keys
   □ Update webhook URLs
   □ Configure SSL certificates
   □ Set up monitoring and logging
   □ Test with real payment methods
   □ Configure backup and recovery

📞 SUPPORT:
   For questions or issues, contact the development team.
`);
}

/**
 * Main function
 */
async function main() {
  await setupStripeIntegration();
}

// Run the setup
if (require.main === module) {
  main();
}

export { setupStripeIntegration };
