import { calendarService } from '../services/calendar.service';
import { calendarHelpers } from '../services/aiResponse';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

/**
 * Comprehensive test suite for Google Calendar integration
 */
class CalendarIntegrationTester {
  private testVenueId: string = 'test-venue-calendar-integration';
  private testResults: { [key: string]: boolean } = {};

  /**
   * Run all calendar integration tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Google Calendar Integration Tests\n');
    
    try {
      await this.testCalendarConnectionStatus();
      await this.testAvailabilityChecking();
      await this.testTourRequestNotification();
      await this.testCalendarEventCreation();
      await this.testErrorHandling();
      await this.testFallbackMechanisms();
      
      this.printTestResults();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    } finally {
      await prisma.$disconnect();
    }
  }

  /**
   * Test calendar connection status checking
   */
  private async testCalendarConnectionStatus(): Promise<void> {
    console.log('📅 Testing Calendar Connection Status...');
    
    try {
      // Test with non-existent venue (should return false)
      const notConnected = await calendarService.isCalendarConnected('non-existent-venue');
      this.testResults['connection_status_false'] = !notConnected;
      
      console.log(`  ✅ Non-connected venue check: ${!notConnected ? 'PASS' : 'FAIL'}`);
      
      // Test connection status for test venue
      const connectionStatus = await calendarService.isCalendarConnected(this.testVenueId);
      this.testResults['connection_status_check'] = true; // Just testing it doesn't throw
      
      console.log(`  ✅ Connection status check: PASS (Status: ${connectionStatus})`);
      
    } catch (error) {
      console.log(`  ❌ Connection status test failed: ${error}`);
      this.testResults['connection_status_check'] = false;
    }
  }

  /**
   * Test availability checking functionality
   */
  private async testAvailabilityChecking(): Promise<void> {
    console.log('\n📆 Testing Availability Checking...');
    
    try {
      const testDate = new Date();
      testDate.setDate(testDate.getDate() + 30); // 30 days from now
      const endDate = new Date(testDate);
      endDate.setDate(endDate.getDate() + 1);
      
      // Test calendar service availability check
      const availability = await calendarService.getAvailability(
        this.testVenueId,
        testDate,
        endDate
      );
      
      this.testResults['availability_check'] = typeof availability.available === 'boolean';
      console.log(`  ✅ Calendar availability check: ${this.testResults['availability_check'] ? 'PASS' : 'FAIL'}`);
      
      // Test AI helper availability check
      const helperResult = await calendarHelpers.checkAvailability(
        this.testVenueId,
        testDate.toISOString().split('T')[0]
      );
      
      this.testResults['helper_availability_check'] = 
        typeof helperResult.available === 'boolean' &&
        typeof helperResult.hasCalendarIntegration === 'boolean';
      
      console.log(`  ✅ Helper availability check: ${this.testResults['helper_availability_check'] ? 'PASS' : 'FAIL'}`);
      console.log(`    - Available: ${helperResult.available}`);
      console.log(`    - Has Calendar Integration: ${helperResult.hasCalendarIntegration}`);
      
    } catch (error) {
      console.log(`  ❌ Availability checking failed: ${error}`);
      this.testResults['availability_check'] = false;
      this.testResults['helper_availability_check'] = false;
    }
  }

  /**
   * Test tour request notification functionality
   */
  private async testTourRequestNotification(): Promise<void> {
    console.log('\n📧 Testing Tour Request Notifications...');
    
    try {
      const testClientInfo = {
        name: 'Test Client',
        email: '<EMAIL>',
        phone: '+1234567890',
        eventDate: '2024-06-15',
        guestCount: 150,
        message: 'Test tour request from integration test'
      };
      
      // Test calendar service notification
      const notificationResult = await calendarService.notifyVenueStaff(
        this.testVenueId,
        {
          ...testClientInfo,
          eventDate: new Date(testClientInfo.eventDate)
        }
      );
      
      this.testResults['notification_service'] = true; // If no error thrown
      console.log(`  ✅ Calendar service notification: PASS`);
      
      // Test AI helper tour request
      const helperResult = await calendarHelpers.sendTourRequest(
        this.testVenueId,
        testClientInfo
      );
      
      this.testResults['helper_tour_request'] = helperResult;
      console.log(`  ✅ Helper tour request: ${helperResult ? 'PASS' : 'FAIL'}`);
      
    } catch (error) {
      console.log(`  ❌ Tour request notification failed: ${error}`);
      this.testResults['notification_service'] = false;
      this.testResults['helper_tour_request'] = false;
    }
  }

  /**
   * Test calendar event creation
   */
  private async testCalendarEventCreation(): Promise<void> {
    console.log('\n📝 Testing Calendar Event Creation...');
    
    try {
      const testEvent = {
        summary: 'Test Wedding - Integration Test',
        description: 'This is a test event created by the integration test suite',
        startTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000 + 4 * 60 * 60 * 1000), // 4 hours later
        attendeeEmail: '<EMAIL>',
        attendeeName: 'Test Client'
      };
      
      const eventId = await calendarService.createBookingEvent(
        this.testVenueId,
        testEvent
      );
      
      this.testResults['event_creation'] = !!eventId;
      console.log(`  ✅ Event creation: ${eventId ? 'PASS' : 'FAIL'}`);
      
      if (eventId) {
        console.log(`    - Created event ID: ${eventId}`);
      }
      
    } catch (error) {
      console.log(`  ❌ Event creation failed: ${error}`);
      this.testResults['event_creation'] = false;
    }
  }

  /**
   * Test error handling scenarios
   */
  private async testErrorHandling(): Promise<void> {
    console.log('\n🚨 Testing Error Handling...');
    
    try {
      // Test with invalid date range
      const invalidStart = new Date('2024-12-31');
      const invalidEnd = new Date('2024-01-01'); // End before start
      
      try {
        await calendarService.getAvailability(this.testVenueId, invalidStart, invalidEnd);
        this.testResults['error_handling_dates'] = false; // Should have thrown error
      } catch (error) {
        this.testResults['error_handling_dates'] = true; // Correctly handled error
      }
      
      console.log(`  ✅ Invalid date range handling: ${this.testResults['error_handling_dates'] ? 'PASS' : 'FAIL'}`);
      
      // Test with invalid venue ID
      try {
        await calendarHelpers.checkAvailability('', 'invalid-date');
        this.testResults['error_handling_venue'] = true; // Should handle gracefully
      } catch (error) {
        this.testResults['error_handling_venue'] = true; // Any handling is acceptable
      }
      
      console.log(`  ✅ Invalid venue ID handling: ${this.testResults['error_handling_venue'] ? 'PASS' : 'FAIL'}`);
      
    } catch (error) {
      console.log(`  ❌ Error handling test failed: ${error}`);
      this.testResults['error_handling_dates'] = false;
      this.testResults['error_handling_venue'] = false;
    }
  }

  /**
   * Test fallback mechanisms when calendar is not connected
   */
  private async testFallbackMechanisms(): Promise<void> {
    console.log('\n🔄 Testing Fallback Mechanisms...');
    
    try {
      // Test availability check for venue without calendar integration
      const fallbackResult = await calendarHelpers.checkAvailability(
        'venue-without-calendar',
        '2024-06-15'
      );
      
      this.testResults['fallback_availability'] = 
        typeof fallbackResult.available === 'boolean' &&
        fallbackResult.hasCalendarIntegration === false;
      
      console.log(`  ✅ Fallback availability check: ${this.testResults['fallback_availability'] ? 'PASS' : 'FAIL'}`);
      console.log(`    - Uses database fallback: ${!fallbackResult.hasCalendarIntegration}`);
      
    } catch (error) {
      console.log(`  ❌ Fallback mechanism test failed: ${error}`);
      this.testResults['fallback_availability'] = false;
    }
  }

  /**
   * Print comprehensive test results
   */
  private printTestResults(): void {
    console.log('\n📊 Test Results Summary');
    console.log('========================');
    
    const totalTests = Object.keys(this.testResults).length;
    const passedTests = Object.values(this.testResults).filter(result => result).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%\n`);
    
    // Detailed results
    Object.entries(this.testResults).forEach(([testName, result]) => {
      const status = result ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${testName.replace(/_/g, ' ').toUpperCase()}`);
    });
    
    console.log('\n🎯 Integration Test Complete!');
    
    if (failedTests > 0) {
      console.log('\n⚠️  Some tests failed. Please review the implementation and fix any issues.');
      console.log('   Check the error messages above for specific failure details.');
    } else {
      console.log('\n🎉 All tests passed! Google Calendar integration is working correctly.');
    }
  }
}

/**
 * Run the calendar integration tests
 */
async function runCalendarTests(): Promise<void> {
  const tester = new CalendarIntegrationTester();
  await tester.runAllTests();
}

// Run tests if this file is executed directly
if (require.main === module) {
  runCalendarTests().catch(console.error);
}

export { runCalendarTests, CalendarIntegrationTester };