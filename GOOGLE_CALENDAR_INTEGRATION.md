# Google Calendar Integration for Evoque Venue Booking Chatbot

This document provides comprehensive instructions for implementing and using the Google Calendar integration feature in the Evoque venue booking chatbot.

## 🎯 Overview

The Google Calendar integration enables:
- **Real-time availability checking** by syncing with venue Google Calendars
- **Automated booking notifications** to venue staff
- **Tour request management** with client contact information
- **Seamless calendar event creation** for confirmed bookings
- **Staff notification system** for follow-up calls

## 🚀 Features Implemented

### 1. Calendar Connection Management
- OAuth 2.0 authentication flow for secure Google Calendar access
- Connection status monitoring for each venue
- Easy disconnect/reconnect functionality

### 2. Real-time Availability Checking
- Query venue availability for specific date ranges
- Automatic fallback to database availability if calendar is disconnected
- Conflict prevention for double bookings

### 3. Automated Booking System
- Create calendar events directly from booking requests
- Include client information and event details
- Send calendar invitations to clients

### 4. Staff Notification System
- Automated email alerts when clients request tours
- Include complete client contact information
- Customizable notification templates

## 🛠️ Setup Instructions

### Step 1: Google Cloud Console Setup

1. **Create a Google Cloud Project**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Note your Project ID

2. **Enable Google Calendar API**
   - Navigate to "APIs & Services" > "Library"
   - Search for "Google Calendar API"
   - Click "Enable"

3. **Create OAuth 2.0 Credentials**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application"
   - Add authorized redirect URIs:
     - `http://localhost:3000/auth/google/callback` (development)
     - `https://yourdomain.com/auth/google/callback` (production)

4. **Configure OAuth Consent Screen**
   - Go to "APIs & Services" > "OAuth consent screen"
   - Fill in application information
   - Add scopes: `https://www.googleapis.com/auth/calendar`
   - Add test users for development

### Step 2: Environment Configuration

Add the following variables to your `.env` file:

```env
# Google Calendar API Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback
```

### Step 3: Install Dependencies

Run the following command in the `evoque-api` directory:

```bash
npm install googleapis @types/googleapis
```

### Step 4: Database Migration (if needed)

If you need to store calendar tokens in the database, add the following to your Prisma schema:

```prisma
model Venue {
  // ... existing fields
  googleCalendarToken    String?
  googleRefreshToken     String?
  calendarConnectedAt    DateTime?
  calendarId             String?
}
```

Then run:
```bash
npx prisma migrate dev --name add-calendar-fields
```

## 📖 Usage Guide

### For Venue Staff

#### Connecting Google Calendar

1. **Generate Authorization URL**
   ```graphql
   mutation {
     generateCalendarAuthUrl(venueId: "venue_id_here")
   }
   ```

2. **Visit the returned URL** and authorize the application

3. **Complete the connection** using the callback code:
   ```graphql
   mutation {
     handleCalendarAuthCallback(
       code: "authorization_code_from_google"
       venueId: "venue_id_here"
     )
   }
   ```

#### Checking Connection Status

```graphql
query {
  venueCalendarStatus(venueId: "venue_id_here") {
    connected
    connectedAt
    calendarId
  }
}
```

#### Disconnecting Calendar

```graphql
mutation {
  disconnectCalendar(venueId: "venue_id_here")
}
```

### For Chatbot Integration

#### Checking Venue Availability

```graphql
query {
  venueAvailability(
    venueId: "venue_id_here"
    startDate: "2024-01-01"
    endDate: "2024-01-31"
  ) {
    available
    events {
      id
      summary
      start
      end
      description
    }
  }
}
```

#### Creating Booking Events

```graphql
mutation {
  createCalendarEvent(
    venueId: "venue_id_here"
    eventDetails: {
      summary: "Wedding Reception - Smith & Johnson"
      description: "150 guests, outdoor ceremony"
      startTime: "2024-06-15T18:00:00Z"
      endTime: "2024-06-15T23:00:00Z"
      attendeeEmail: "<EMAIL>"
      attendeeName: "Sarah Smith"
    }
  )
}
```

#### Sending Tour Requests

```graphql
mutation {
  sendTourRequest(
    venueId: "venue_id_here"
    clientInfo: {
      name: "John Doe"
      email: "<EMAIL>"
      phone: "+1234567890"
      eventDate: "2024-06-15"
      guestCount: 150
      message: "Interested in outdoor wedding package"
    }
  )
}
```

## 🔧 Technical Implementation

### Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Chatbot UI    │───▶│   GraphQL API    │───▶│ Calendar Service│
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Database       │    │ Google Calendar │
                       │   (Prisma)       │    │      API        │
                       └──────────────────┘    └─────────────────┘
```

### Key Components

1. **CalendarService** (`src/services/calendar.service.ts`)
   - Handles Google Calendar API interactions
   - Manages OAuth authentication flow
   - Provides availability checking and event creation

2. **GraphQL Schema** (`src/graphql/schema/calendar.schema.ts`)
   - Defines types and operations for calendar functionality
   - Includes input validation and error handling

3. **GraphQL Resolvers** (`src/graphql/resolvers/calendar.resolvers.ts`)
   - Implements business logic for calendar operations
   - Handles authentication and authorization

### Error Handling

The implementation includes comprehensive error handling for:
- Invalid OAuth tokens (automatic refresh)
- Network connectivity issues
- Google API rate limits
- Invalid date ranges
- Missing permissions

### Security Considerations

- OAuth 2.0 for secure authentication
- Token encryption in database storage
- Input validation and sanitization
- Rate limiting on API endpoints
- Proper error message sanitization

## 🧪 Testing

### Manual Testing Checklist

- [ ] OAuth flow completes successfully
- [ ] Calendar connection status updates correctly
- [ ] Availability checking returns accurate results
- [ ] Event creation works with all required fields
- [ ] Tour request notifications are sent
- [ ] Error handling works for invalid inputs
- [ ] Token refresh works automatically
- [ ] Disconnect functionality works properly

### Test Queries

Use these GraphQL queries in your GraphQL playground for testing:

```graphql
# Test connection status
query TestConnection {
  venueCalendarStatus(venueId: "test-venue-id") {
    connected
    connectedAt
    calendarId
  }
}

# Test availability check
query TestAvailability {
  venueAvailability(
    venueId: "test-venue-id"
    startDate: "2024-01-01"
    endDate: "2024-01-07"
  ) {
    available
    events {
      summary
      start
      end
    }
  }
}
```

## 🚨 Troubleshooting

### Common Issues

1. **"Invalid credentials" error**
   - Verify `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET` in `.env`
   - Check that OAuth consent screen is properly configured

2. **"Redirect URI mismatch" error**
   - Ensure `GOOGLE_REDIRECT_URI` matches the one configured in Google Cloud Console
   - Check for trailing slashes and protocol (http vs https)

3. **"Calendar not found" error**
   - Verify the venue has connected their Google Calendar
   - Check that the calendar ID is correctly stored

4. **"Insufficient permissions" error**
   - Ensure the OAuth scope includes `https://www.googleapis.com/auth/calendar`
   - Re-authorize the application if needed

### Debug Mode

Enable debug logging by setting:
```env
LOG_LEVEL=debug
```

## 📋 Deployment Checklist

- [ ] Environment variables configured in production
- [ ] Google Cloud Console OAuth settings updated for production domain
- [ ] Database migrations applied
- [ ] Dependencies installed (`googleapis`)
- [ ] SSL certificate configured for OAuth redirect
- [ ] Error monitoring configured
- [ ] Rate limiting configured

## 🔄 Future Enhancements

- **Multi-calendar support** for venues with multiple event spaces
- **Recurring event handling** for regular bookings
- **Calendar sync** for existing bookings in the database
- **Advanced conflict resolution** with automatic rescheduling suggestions
- **Integration with other calendar providers** (Outlook, Apple Calendar)
- **Automated follow-up scheduling** based on tour requests

## 📞 Support

For technical support or questions about the Google Calendar integration:

1. Check this documentation first
2. Review the troubleshooting section
3. Check the application logs for error details
4. Contact the development team with specific error messages and steps to reproduce

---

**Last Updated:** December 2024  
**Version:** 1.0.0  
**Compatibility:** Node.js 18+, Google Calendar API v3