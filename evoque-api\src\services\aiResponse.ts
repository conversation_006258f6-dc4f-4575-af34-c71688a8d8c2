// AI Response Service - Instant Intelligent Responses
// Handles the 5-minute response guarantee that drives 9x higher conversions

import OpenAI from 'openai';
import { PrismaClient } from '@prisma/client';
import { env } from '../config/env';
import { calendarService } from './calendar.service';

const openai = new OpenAI({
  apiKey: env.OPENROUTER_API_KEY,
  baseURL: 'https://openrouter.ai/api/v1',
  defaultHeaders: {
    'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'https://evoque.digital',
    'X-Title': 'Evoque Wedding Platform'
  }
});

const prisma = new PrismaClient();

interface VenueContext {
  name: string;
  description: string;
  pricing: {
    packages: Array<{
      name: string;
      price: string;
      includes: string[];
    }>;
    starting: string;
  };
  capacity: {
    min: number;
    max: number;
  };
  amenities: string[];
  policies: {
    catering: string;
    alcohol: string;
    music: string;
    decoration: string;
  };
  availability: any;
  personality: {
    tone: 'formal' | 'friendly' | 'luxury' | 'casual';
    emphasis: string[];
  };
}

interface ConversationContext {
  leadId: string;
  messages: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
  }>;
  leadScore?: number;
  eventDetails?: any;
}

export class AIResponseService {
  private venueId: string;
  private venueContext: VenueContext | null = null;

  constructor(venueId: string) {
    this.venueId = venueId;
  }

  async initialize() {
    const venue = await prisma.venue.findUnique({
      where: { id: this.venueId }
    });
    
    this.venueContext = venue;
  }

  async generateInstantResponse(
    inquiry: string,
    conversationContext?: ConversationContext
  ): Promise<{
    response: string;
    suggestedFollowUp: string;
    extractedDetails: any;
  }> {
    if (!this.venueContext) await this.initialize();

    // Extract key details from inquiry
    const extractedDetails = await this.extractEventDetails(inquiry);
    
    // Build system prompt with venue personality
    const systemPrompt = this.buildSystemPrompt();
    
    // Generate contextual response
    const messages = [
      { role: 'system' as const, content: systemPrompt },
      ...(conversationContext?.messages || []),
      { role: 'user' as const, content: inquiry }
    ];

    const completion = await openai.chat.completions.create({
      model: env.OPENROUTER_MODEL,
      messages,
      temperature: 0.7,
      max_tokens: 500,
      functions: [
        {
          name: 'structureResponse',
          description: 'Structure the response with greeting, answer, and next steps',
          parameters: {
            type: 'object',
            properties: {
              greeting: { type: 'string' },
              mainResponse: { type: 'string' },
              questionsToAsk: { type: 'array', items: { type: 'string' } },
              nextSteps: { type: 'string' },
              bookingIncentive: { type: 'string' }
            }
          }
        }
      ],
      function_call: { name: 'structureResponse' }
    });

    const functionResponse = JSON.parse(
      completion.choices[0].message.function_call?.arguments || '{}'
    );

    // Format the response
    const formattedResponse = this.formatResponse(functionResponse, extractedDetails);
    
    // Generate follow-up suggestion for venue staff
    const suggestedFollowUp = await this.generateHumanFollowUp(
      inquiry,
      formattedResponse,
      extractedDetails
    );

    return {
      response: formattedResponse,
      suggestedFollowUp,
      extractedDetails
    };
  }

  private buildSystemPrompt(): string {
    const venue = this.venueContext!;
    
    return `You are an AI assistant for ${venue.name}, a beautiful wedding venue. 

Your personality: ${venue.personality.tone} and ${venue.personality.emphasis.join(', ')}.

Key Information:
- Capacity: ${venue.capacity.min}-${venue.capacity.max} guests
- Starting price: ${venue.pricing.starting}
- Unique features: ${venue.amenities.join(', ')}

Packages:
${venue.pricing.packages.map(p => 
  `- ${p.name}: ${p.price} (includes: ${p.includes.join(', ')})`
).join('\n')}

Guidelines:
1. Be warm and enthusiastic about their special day
2. Answer questions directly but always guide toward booking a tour
3. Mention availability only if you can confirm the date is open
4. Highlight what makes us unique compared to other venues
5. Create urgency without being pushy
6. If budget seems lower than our range, emphasize value and payment plans

IMPORTANT: Keep responses under 150 words, conversational, and end with a clear next step.`;
  }

  private async extractEventDetails(inquiry: string): Promise<any> {
    const completion = await openai.chat.completions.create({
      model: env.OPENROUTER_MODEL,
      messages: [
        {
          role: 'system',
          content: 'Extract wedding event details from the inquiry. Return null for any detail not mentioned.'
        },
        { role: 'user', content: inquiry }
      ],
      functions: [
        {
          name: 'extractDetails',
          description: 'Extract event details from inquiry',
          parameters: {
            type: 'object',
            properties: {
              eventDate: { type: 'string', format: 'date' },
              guestCount: { type: 'number' },
              budget: { type: 'string' },
              eventType: { type: 'string' },
              specificQuestions: { type: 'array', items: { type: 'string' } }
            }
          }
        }
      ],
      function_call: { name: 'extractDetails' }
    });

    return JSON.parse(
      completion.choices[0].message.function_call?.arguments || '{}'
    );
  }

  private formatResponse(structured: any, details: any): string {
    let response = structured.greeting + '\n\n';
    response += structured.mainResponse + '\n\n';
    
    if (structured.questionsToAsk?.length > 0) {
      response += 'To help me provide the perfect recommendation:\n';
      structured.questionsToAsk.forEach((q: string) => {
        response += `• ${q}\n`;
      });
      response += '\n';
    }
    
    response += structured.nextSteps;
    
    if (details.eventDate && this.isDateSoon(details.eventDate)) {
      response += '\n\n' + (structured.bookingIncentive || 
        '✨ Book a tour this week and receive 10% off your venue rental!');
    }
    
    return response;
  }

  private isDateSoon(dateStr: string): boolean {
    const eventDate = new Date(dateStr);
    const daysUntil = (eventDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24);
    return daysUntil < 180; // Less than 6 months
  }

  private async generateHumanFollowUp(
    inquiry: string,
    aiResponse: string,
    details: any
  ): Promise<string> {
    const completion = await openai.chat.completions.create({
      model: env.OPENROUTER_MODEL,
      messages: [
        {
          role: 'system',
          content: `Generate a personalized follow-up message for venue staff to send 
          within 1 hour of the AI response. Make it personal and reference specific 
          details from their inquiry.`
        },
        {
          role: 'user',
          content: `Inquiry: ${inquiry}\n\nAI Response: ${aiResponse}\n\nDetails: ${JSON.stringify(details)}`
        }
      ],
      max_tokens: 200
    });

    return completion.choices[0].message.content || '';
  }

  async saveConversation(
    leadId: string,
    message: string,
    response: string
  ): Promise<void> {
    await prisma.conversation.upsert({
      where: { leadId },
      update: {
        messages: {
          push: [
            { role: 'user', content: message, timestamp: new Date() },
            { role: 'assistant', content: response, timestamp: new Date() }
          ]
        }
      },
      create: {
        leadId,
        messages: [
          { role: 'user', content: message, timestamp: new Date() },
          { role: 'assistant', content: response, timestamp: new Date() }
        ]
      }
    });
  }
}

// Enhanced response templates with calendar integration
export const responseTemplates = {
  availability: (date: string, available: boolean, hasCalendarIntegration: boolean = false) => 
    available 
      ? `Wonderful news! We do have ${date} available${hasCalendarIntegration ? ' (confirmed through our real-time calendar)' : ''}. Since popular dates book quickly, I'd love to schedule a tour so you can see how perfect our venue would be for your special day.`
      : `While ${date} is already booked${hasCalendarIntegration ? ' (checked against our live calendar)' : ''}, we have some beautiful dates available nearby. Let me show you our calendar and find the perfect date for your celebration.`,
  
  pricing: (startingPrice: string) =>
    `Our packages start at ${startingPrice} and include everything you need for an unforgettable celebration. Each package can be customized to your vision. The best way to find your perfect package is to visit us - shall we schedule a tour?`,
  
  capacity: (min: number, max: number, guestCount: number) =>
    `Perfect! With ${guestCount} guests, you'll fit beautifully in our venue (we accommodate ${min}-${max} guests). Your celebration will feel intimate yet spacious. When can you visit to see the space in person?`,
    
  tourRequest: (clientName: string, eventDate?: string) =>
    `Thank you ${clientName}! I've sent your tour request to our venue coordinator${eventDate ? ` along with your preferred event date of ${eventDate}` : ''}. They'll contact you within 2 hours during business hours to schedule your personal tour. In the meantime, feel free to ask me any questions about our venue!`,
    
  calendarSync: () =>
    `Great news! Our calendar is synced in real-time with Google Calendar, so you're seeing our most up-to-date availability. This ensures no double bookings and gives you confidence in the dates we show as available.`
};

// Calendar integration helper functions
export const calendarHelpers = {
  /**
   * Check real-time availability for a specific date
   */
  async checkAvailability(venueId: string, date: string): Promise<{ available: boolean; hasCalendarIntegration: boolean }> {
    try {
      const eventDate = new Date(date);
      const nextDay = new Date(eventDate);
      nextDay.setDate(nextDay.getDate() + 1);
      
      // Check if venue has calendar integration
      const hasCalendarIntegration = await calendarService.isCalendarConnected(venueId);
      
      if (hasCalendarIntegration) {
        // Use real-time calendar data
        const availability = await calendarService.getAvailability(venueId, eventDate, nextDay);
        return {
          available: availability.available,
          hasCalendarIntegration: true
        };
      } else {
        // Fallback to database availability
        const prisma = new PrismaClient();
        const dayOfWeek = eventDate.getDay();
        const venueAvailability = await prisma.venueAvailability.findFirst({
          where: {
            venueId,
            dayOfWeek,
            isAvailable: true
          }
        });
        
        // Check for existing bookings
        const existingBooking = await prisma.eventSpaceAvailability.findFirst({
          where: {
            eventSpace: {
              venueId
            },
            date: eventDate,
            isBooked: true
          }
        });
        
        return {
          available: !!venueAvailability && !existingBooking,
          hasCalendarIntegration: false
        };
      }
    } catch (error) {
      console.error('Error checking availability:', error);
      return {
        available: false,
        hasCalendarIntegration: false
      };
    }
  },
  
  /**
   * Send tour request with calendar integration
   */
  async sendTourRequest(venueId: string, clientInfo: {
    name: string;
    email: string;
    phone?: string;
    eventDate?: string;
    guestCount?: number;
    message?: string;
  }): Promise<boolean> {
    try {
      await calendarService.notifyVenueStaff(venueId, {
        name: clientInfo.name,
        email: clientInfo.email,
        phone: clientInfo.phone,
        eventDate: clientInfo.eventDate ? new Date(clientInfo.eventDate) : undefined,
        guestCount: clientInfo.guestCount,
        message: clientInfo.message
      });
      return true;
    } catch (error) {
      console.error('Error sending tour request:', error);
      return false;
    }
  }
};
