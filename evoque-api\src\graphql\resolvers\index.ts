import { userResolvers } from './user.resolvers';
import { venueResolvers } from './venue.resolvers';
import { knowledgeBaseResolvers } from './knowledgeBase.resolvers';
import { inquiryResolvers } from './inquiry.resolvers';
import { leadResolvers } from './lead.resolvers';
import { mediaResolvers } from './media.resolvers';
import { analyticsResolvers } from './analytics.resolvers';
import { authResolvers } from './auth.resolvers';
import { aiResolvers } from './ai.resolvers';
import { paymentResolvers } from './payment.resolvers';
import { adminResolvers } from './admin.resolvers';
import { calendarResolvers } from './calendar.resolvers';
import { GraphQLDateTime, GraphQLDate } from 'graphql-scalars';
import { GraphQLJSONObject } from 'graphql-type-json';

// Define scalar resolvers
const scalarResolvers = {
  DateTime: GraphQLDateTime,
  Date: GraphQLDate,
  JSON: GraphQLJSONObject,
};

// Merge all resolvers
export const resolvers = {
  // Scalar resolvers
  ...scalarResolvers,
  
  // Query resolvers
  Query: {
    _: () => true, // Placeholder resolver for empty Query type
    ...userResolvers.Query,
    ...venueResolvers.Query,
    ...knowledgeBaseResolvers.Query,
    ...inquiryResolvers.Query,
    ...leadResolvers.Query,
    ...mediaResolvers.Query,
    ...analyticsResolvers.Query,
    ...aiResolvers.Query,
    ...paymentResolvers.Query,
    ...adminResolvers.Query,
    ...calendarResolvers.Query,
  },
  
  // Mutation resolvers
  Mutation: {
    _: () => true, // Placeholder resolver for empty Mutation type
    ...authResolvers.Mutation,
    ...userResolvers.Mutation,
    ...venueResolvers.Mutation,
    ...knowledgeBaseResolvers.Mutation,
    ...inquiryResolvers.Mutation,
    ...leadResolvers.Mutation,
    ...mediaResolvers.Mutation,
    ...aiResolvers.Mutation,
    ...paymentResolvers.Mutation,
    ...adminResolvers.Mutation,
    ...calendarResolvers.Mutation,
  },
  
  // Subscription resolvers
  Subscription: {
    _: () => true, // Placeholder resolver for empty Subscription type
    ...inquiryResolvers.Subscription,
    ...leadResolvers.Subscription,
  },
  
  // Type resolvers
  User: userResolvers.User,
  Venue: venueResolvers.Venue,
  KnowledgeBaseItem: knowledgeBaseResolvers.KnowledgeBaseItem,
  KnowledgeBaseCategory: knowledgeBaseResolvers.KnowledgeBaseCategory,
  Inquiry: inquiryResolvers.Inquiry,
  Lead: leadResolvers.Lead,
  LeadTask: leadResolvers.LeadTask,
  MediaAsset: mediaResolvers.MediaAsset,
  MediaCategory: mediaResolvers.MediaCategory,
  
  // Calendar type resolvers
  Venue: {
    ...venueResolvers.Venue,
    ...calendarResolvers.Venue,
  },
};