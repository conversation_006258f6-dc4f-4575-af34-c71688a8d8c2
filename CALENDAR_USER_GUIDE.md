# 📅 Google Calendar Integration - User Guide

This guide provides step-by-step instructions for venue staff and clients on how to use the Google Calendar integration features in the Evoque venue booking system.

## 🎯 For Venue Staff

### Getting Started with Calendar Integration

#### Step 1: Connect Your Google Calendar

1. **Access Your Venue Dashboard**
   - Log into your Evoque venue management portal
   - Navigate to "Settings" > "Calendar Integration"

2. **Initiate Calendar Connection**
   - Click the "Connect Google Calendar" button
   - You'll be redirected to Google's authorization page

3. **Authorize Access**
   - Sign in with your Google account (the one with your venue calendar)
   - Review the permissions requested:
     - ✅ View your calendar events
     - ✅ Create new calendar events
     - ✅ Modify existing events
   - Click "Allow" to grant access

4. **Confirm Connection**
   - You'll be redirected back to Evoque
   - You should see a green "✅ Connected" status
   - Your calendar will now sync with the booking system

#### Step 2: Managing Your Calendar Connection

**Check Connection Status:**
- Green dot (🟢): Calendar is connected and syncing
- Red dot (🔴): Calendar is disconnected or has issues
- Yellow dot (🟡): Connection needs refresh

**Refresh Connection:**
If you see connection issues:
1. Click "Refresh Connection"
2. Re-authorize if prompted
3. Wait for the green status confirmation

**Disconnect Calendar:**
To disconnect your calendar:
1. Click "Disconnect Calendar"
2. Confirm the action
3. Your calendar will stop syncing (existing events remain)

### Managing Bookings and Availability

#### Understanding Availability Display

**Available Dates (Green):**
- No conflicting events in your calendar
- Venue is open for bookings
- Clients can request tours or bookings

**Busy Dates (Red):**
- Existing events in your calendar
- Venue is not available for new bookings
- Clients will see "Not Available" message

**Partially Available (Yellow):**
- Some time slots are free
- Clients can see specific available hours
- Perfect for venues with multiple event spaces

#### Creating Booking Events

When a booking is confirmed:
1. **Automatic Event Creation**
   - Event appears in your Google Calendar
   - Includes client contact information
   - Contains event details and requirements

2. **Event Details Include:**
   - Client name and contact information
   - Event type (wedding, corporate, etc.)
   - Guest count
   - Special requirements
   - Catering preferences
   - Setup/breakdown times

3. **Calendar Invitations**
   - Clients automatically receive calendar invites
   - Includes venue address and contact info
   - Contains preparation timeline

### Handling Tour Requests

#### Receiving Tour Notifications

When clients request tours, you'll receive:

**Email Notifications:**
- Subject: "📅 New Tour Request - [Venue Name]"
- Client contact information
- Preferred tour dates/times
- Event details and requirements
- Direct links to respond

**Dashboard Alerts:**
- Real-time notifications in your venue dashboard
- Priority indicators for urgent requests
- Quick action buttons for responses

#### Responding to Tour Requests

1. **Review Client Information**
   - Name, email, phone number
   - Event date and guest count
   - Special requirements or questions
   - Budget range (if provided)

2. **Check Your Availability**
   - View your calendar for the requested dates
   - Consider setup/breakdown time requirements
   - Check for any conflicts or constraints

3. **Respond Promptly**
   - Use the "Quick Response" buttons:
     - ✅ "Schedule Tour" - Opens calendar booking
     - 📞 "Call Client" - Logs call attempt
     - 📧 "Send Email" - Opens email template
     - ❌ "Decline" - Sends polite decline message

4. **Schedule the Tour**
   - Select available time slots
   - Send calendar invitation to client
   - Include tour agenda and what to bring
   - Add venue contact person details

### Best Practices for Venue Staff

#### Calendar Management

**Keep Your Calendar Updated:**
- ✅ Block out personal time and holidays
- ✅ Include setup and breakdown time for events
- ✅ Add buffer time between events
- ✅ Mark maintenance or renovation periods

**Use Descriptive Event Titles:**
- ❌ "Wedding"
- ✅ "Smith-Johnson Wedding (150 guests)"
- ✅ "Corporate Event - Tech Conference (200 attendees)"

**Include Important Details:**
- Client contact information
- Event timeline and requirements
- Vendor coordination notes
- Special setup instructions

#### Client Communication

**Response Time Goals:**
- 🎯 Tour requests: Within 2 hours during business hours
- 🎯 Booking inquiries: Within 1 hour during business hours
- 🎯 Urgent requests: Within 30 minutes

**Professional Communication:**
- Use the provided email templates
- Include your direct contact information
- Provide clear next steps
- Follow up within 24 hours of tours

---

## 👰 For Clients (Potential Venue Renters)

### How to Check Venue Availability

#### Using the Chatbot

1. **Start a Conversation**
   - Visit the venue's website
   - Click the chat icon (usually bottom-right)
   - Type "Hi" or "Check availability"

2. **Provide Your Event Details**
   The chatbot will ask for:
   - Event date (or date range)
   - Expected guest count
   - Event type (wedding, corporate, etc.)
   - Time of day preferences

3. **View Real-Time Availability**
   - ✅ Green dates: Fully available
   - ⚠️ Yellow dates: Partially available
   - ❌ Red dates: Not available
   - 📅 Alternative dates will be suggested

#### Understanding Availability Results

**"Fully Available" means:**
- The venue is completely free on your date
- No conflicting events scheduled
- All event spaces are available
- You can proceed with booking

**"Partially Available" means:**
- Some time slots are free
- Certain event spaces might be available
- You may need to adjust timing
- Contact venue for specific details

**"Not Available" means:**
- The venue is booked on your date
- Consider alternative dates
- Join the waitlist for cancellations
- Explore nearby available dates

### Requesting a Venue Tour

#### Through the Chatbot

1. **Express Interest**
   - Say "I'd like to schedule a tour"
   - Or "Can I visit the venue?"

2. **Provide Your Information**
   - Full name
   - Email address
   - Phone number
   - Preferred tour dates/times

3. **Share Event Details**
   - Event date
   - Guest count
   - Event type
   - Special requirements
   - Budget range (optional)

4. **Receive Confirmation**
   - Instant confirmation message
   - Email with tour details
   - Calendar invitation
   - Venue contact information

#### What to Expect

**Response Time:**
- Immediate chatbot confirmation
- Venue staff response within 2 hours
- Tour scheduling within 24 hours

**Tour Confirmation Email:**
- Date, time, and duration
- Venue address and parking info
- Contact person details
- What to bring/prepare
- Agenda overview

**Calendar Invitation:**
- Automatic calendar event
- Venue location with map link
- Contact information
- Reminder notifications

### Making a Booking Request

#### Step-by-Step Process

1. **Complete Venue Tour**
   - Visit the venue in person
   - Ask questions about services
   - Review contracts and pricing
   - Take photos/videos if allowed

2. **Submit Booking Request**
   - Through the chatbot or venue portal
   - Provide all event details
   - Include special requirements
   - Submit required documents

3. **Receive Booking Confirmation**
   - Contract and pricing details
   - Payment schedule
   - Event timeline
   - Vendor coordination info

4. **Calendar Integration**
   - Event added to your calendar
   - Venue contact information
   - Important milestone reminders
   - Countdown to your event

### Tips for a Smooth Experience

#### Before Contacting Venues

**Prepare Your Information:**
- ✅ Event date (with 2-3 backup dates)
- ✅ Guest count estimate
- ✅ Budget range
- ✅ Event timeline preferences
- ✅ Special requirements list

**Research the Venue:**
- ✅ Review photos and virtual tours
- ✅ Read recent reviews
- ✅ Check included services
- ✅ Note any restrictions

#### During Venue Tours

**Questions to Ask:**
- What's included in the base package?
- Are there any additional fees?
- What's the cancellation policy?
- Can we bring our own vendors?
- What's the backup plan for weather?
- How many events do you host per day?

**What to Observe:**
- Cleanliness and maintenance
- Staff professionalism
- Parking availability
- Accessibility features
- Photo opportunities
- Sound system quality

#### After Your Tour

**Follow-Up Actions:**
- ✅ Send thank-you email within 24 hours
- ✅ Compare with other venues
- ✅ Check references if needed
- ✅ Review contract details carefully
- ✅ Make decision within agreed timeframe

---

## 🆘 Troubleshooting & Support

### Common Issues and Solutions

#### For Venue Staff

**"Calendar not syncing"**
- Check internet connection
- Refresh the calendar connection
- Re-authorize Google Calendar access
- Contact support if issues persist

**"Missing tour requests"**
- Check email spam/junk folder
- Verify notification settings
- Ensure calendar permissions are correct
- Update contact information

**"Events not appearing in Google Calendar"**
- Verify calendar connection status
- Check Google Calendar permissions
- Refresh the integration
- Try creating a test event

#### For Clients

**"Chatbot not responding"**
- Refresh the webpage
- Clear browser cache
- Try a different browser
- Check internet connection

**"Not receiving confirmation emails"**
- Check spam/junk folder
- Verify email address spelling
- Add venue domain to safe senders
- Contact venue directly if needed

**"Calendar invitation not received"**
- Check all email folders
- Verify calendar app settings
- Try different email address
- Request manual calendar invite

### Getting Help

**For Venue Staff:**
- 📧 Email: <EMAIL>
- 📞 Phone: [Support Number]
- 💬 Live Chat: Available 9 AM - 6 PM EST
- 📚 Knowledge Base: help.evoque.digital

**For Clients:**
- Contact the venue directly using provided information
- Use the chatbot for immediate assistance
- Email venue staff for detailed questions
- Call venue for urgent matters

---

**Need more help?** This guide is regularly updated. Check for the latest version at help.evoque.digital or contact our support team.

**Last Updated:** December 2024  
**Version:** 1.0.0