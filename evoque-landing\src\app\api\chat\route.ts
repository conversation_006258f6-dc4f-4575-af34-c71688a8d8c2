import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

// Initialize OpenAI with OpenRouter
const openai = new OpenAI({
  apiKey: process.env.OPENROUTER_API_KEY || '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',
  baseURL: 'https://openrouter.ai/api/v1',
  defaultHeaders: {
    'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'https://evoque.digital',
    'X-Title': 'Evoque Wedding Platform'
  }
});

// Venue context for the AI (in production, fetch from database)
const VENUE_CONTEXT = {
  name: "Rosewood Manor",
  description: "An elegant wedding venue with stunning gardens and classic architecture",
  capacity: { min: 50, max: 300 },
  pricing: {
    starting: "$5,000",
    packages: [
      { name: "Silver", price: "$5,000", includes: ["Venue", "Tables/Chairs", "Basic Lighting"] },
      { name: "Gold", price: "$10,000", includes: ["Silver items", "Catering", "Bar Service", "Coordinator"] },
      { name: "Platinum", price: "$20,000", includes: ["Gold items", "Premium Catering", "Flowers", "Photography"] }
    ]
  },
  amenities: ["Bridal Suite", "Groom's Room", "Outdoor Ceremony Space", "Indoor Reception Hall", "Full Kitchen"],
  policies: {
    catering: "Preferred vendors or BYO with fee",
    alcohol: "Bar service available",
    music: "DJ or live band welcome until midnight"
  }
};

const SYSTEM_PROMPT = `You are an AI assistant for ${VENUE_CONTEXT.name}, a beautiful wedding venue. 

Your personality: Warm, professional, and enthusiastic about helping couples plan their special day.

Key Information:
- Capacity: ${VENUE_CONTEXT.capacity.min}-${VENUE_CONTEXT.capacity.max} guests
- Starting price: ${VENUE_CONTEXT.pricing.starting}
- Unique features: ${VENUE_CONTEXT.amenities.join(', ')}

Guidelines:
1. Be warm and enthusiastic about their special day
2. Answer questions directly but always guide toward booking a tour
3. Keep responses conversational and under 150 words
4. Create gentle urgency without being pushy
5. If budget seems lower, emphasize value and flexible options
6. Always end with a clear next step or question

Remember: The goal is to qualify leads and encourage tour bookings.`;

export async function POST(request: NextRequest) {
  try {
    const { message, conversationHistory = [], leadInfo = {} } = await request.json();

    // Extract event details from the message
    const eventDetails = await extractEventDetails(message);

    // Build conversation messages
    const messages = [
      { role: 'system' as const, content: SYSTEM_PROMPT },
      ...conversationHistory,
      { role: 'user' as const, content: message }
    ];

    // Generate AI response
    const completion = await openai.chat.completions.create({
      model: process.env.OPENROUTER_MODEL || 'google/gemini-2.5-flash-lite-preview-06-17',
      messages,
      temperature: 0.7,
      max_tokens: 200
    });

    const aiResponse = completion.choices[0].message.content || "I'd love to help you plan your special day! Could you tell me more about your vision?";

    // Generate a suggested follow-up for venue staff
    const followUpSuggestion = generateFollowUpSuggestion(message, eventDetails);

    // Score this interaction (simplified version)
    const qualificationScore = calculateQualificationScore(message, eventDetails, conversationHistory);

    return NextResponse.json({
      response: aiResponse,
      eventDetails,
      followUpSuggestion,
      qualificationScore,
      shouldNotifyStaff: qualificationScore.score > 70
    });

  } catch (error) {
    console.error('Chat API error:', error);
    
    // Fallback response if AI fails
    const fallbackResponse = "Thank you for your interest in Rosewood Manor! I'd love to learn more about your special day. What date are you considering, and how many guests are you expecting?";
    
    return NextResponse.json({
      response: fallbackResponse,
      eventDetails: {},
      followUpSuggestion: "AI service temporarily unavailable - please respond personally",
      qualificationScore: { score: 50, factors: {} },
      shouldNotifyStaff: true
    });
  }
}

async function extractEventDetails(message: string) {
  // Simple extraction (in production, use AI function calling)
  const details: any = {};
  
  // Date extraction
  const dateMatch = message.match(/(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})|(\w+ \d{1,2},? \d{4})/);
  if (dateMatch) details.date = dateMatch[0];
  
  // Guest count extraction
  const guestMatch = message.match(/(\d+)\s*(guests?|people|attendees)/i);
  if (guestMatch) details.guestCount = parseInt(guestMatch[1]);
  
  // Budget extraction
  const budgetMatch = message.match(/\$[\d,]+k?|\d+k?/i);
  if (budgetMatch) details.budget = budgetMatch[0];
  
  return details;
}

function generateFollowUpSuggestion(message: string, eventDetails: any) {
  const suggestions = [];
  
  if (eventDetails.date) {
    suggestions.push(`Confirm availability for ${eventDetails.date}`);
  }
  
  if (eventDetails.guestCount) {
    suggestions.push(`Discuss layout options for ${eventDetails.guestCount} guests`);
  }
  
  if (!eventDetails.budget) {
    suggestions.push("Gently inquire about budget range");
  }
  
  suggestions.push("Offer tour times for this week");
  
  return suggestions.join(" • ");
}

function calculateQualificationScore(message: string, eventDetails: any, history: any[]) {
  let score = 50; // Base score
  
  // Message engagement
  if (message.length > 100) score += 10;
  if (message.includes('?')) score += 5;
  
  // Event details provided
  if (eventDetails.date) score += 15;
  if (eventDetails.guestCount) score += 10;
  if (eventDetails.budget) score += 10;
  
  // Conversation depth
  if (history.length > 2) score += 10;
  
  // Intent signals
  if (message.toLowerCase().includes('tour') || message.toLowerCase().includes('visit')) score += 15;
  if (message.toLowerCase().includes('available') || message.toLowerCase().includes('book')) score += 10;
  
  return {
    score: Math.min(score, 100),
    factors: {
      engagement: message.length > 100,
      hasDate: !!eventDetails.date,
      hasGuestCount: !!eventDetails.guestCount,
      hasBudget: !!eventDetails.budget,
      showsIntent: message.toLowerCase().includes('tour') || message.toLowerCase().includes('book')
    }
  };
}