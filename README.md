# Evoque Wedding Platform

A comprehensive AI-powered wedding venue management system that helps venues capture, manage, and convert leads through intelligent chat widgets and real-time analytics.

## 🏗️ Project Architecture

The Evoque platform consists of five main components:

### Core Components

1. **evoque-api** - Backend API & Services
   - Node.js/TypeScript with GraphQL API
   - Prisma ORM with PostgreSQL database
   - AI-powered chat with OpenAI integration
   - Stripe payment processing
   - Real-time analytics and notifications

2. **evoque-landing** - Marketing Landing Page
   - Next.js 13+ with App Router
   - Wedding-themed UI with animations (Framer Motion, GSAP)
   - Supabase integration for lead capture
   - Mobile-responsive design with Tailwind CSS

3. **evoque-dashboard** - Admin Dashboard
   - Next.js admin interface for venue managers
   - Apollo Client for GraphQL integration
   - Real-time analytics and lead management
   - NextAuth authentication

4. **evoque-widget** - Embeddable Chat Widget
   - Universal embeddable widget for venue websites
   - Platform-specific integrations (WordPress, Wix, Squarespace, etc.)
   - React-based with Webpack bundling
   - CDN-ready deployment

5. **evoque-devops** - Deployment & Infrastructure
   - Docker configurations
   - NGINX configurations
   - Hostinger VPS deployment guides
   - CI/CD pipeline configurations

## ✨ Key Features

- **Universal Widget Installation**: One-line script works on any website platform
- **AI-Powered Chat**: Intelligent responses with OpenAI integration
- **Real-Time Analytics**: Live conversion tracking and performance metrics
- **Multi-Platform Support**: WordPress, Wix, Squarespace, Shopify, Webflow
- **Mobile-Optimized**: Responsive design for all devices
- **Payment Processing**: Integrated Stripe subscription management
- **Lead Management**: Comprehensive inquiry tracking and scoring

## 🚀 Quick Start

### Prerequisites

- Node.js 18.x or higher
- npm or yarn package manager
- PostgreSQL database
- Git

### Development Setup

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd evoque-wedding-platform
   ```

2. **Install dependencies for all components:**
   ```bash
   # Backend API
   cd evoque-api && npm install

   # Landing page
   cd ../evoque-landing && npm install

   # Dashboard
   cd ../evoque-dashboard && npm install

   # Widget
   cd ../evoque-widget && npm install
   ```

3. **Set up environment variables:**
   ```bash
   # Copy example files and configure
   cp evoque-api/.env.example evoque-api/.env
   cp evoque-landing/.env.example evoque-landing/.env.local
   # Configure database, API keys, etc.
   ```

4. **Initialize the database:**
   ```bash
   cd evoque-api
   npx prisma migrate dev
   npx prisma db seed
   ```

5. **Start development servers:**
   ```bash
   # Terminal 1 - API
   cd evoque-api && npm run dev

   # Terminal 2 - Landing page
   cd evoque-landing && npm run dev

   # Terminal 3 - Dashboard
   cd evoque-dashboard && npm run dev
   ```

### Production Deployment

For production deployment, see the comprehensive guides in:
- `evoque-devops/DEPLOYMENT.md` - Complete deployment guide
- `evoque-devops/HOSTINGER_DEPLOYMENT_GUIDE.md` - VPS-specific instructions

## 📊 Project Status

**Current Status**: ✅ **PRODUCTION READY**

The platform is fully implemented with:
- ✅ Complete backend API with GraphQL and REST endpoints
- ✅ Universal widget system with platform integrations
- ✅ Real-time analytics and conversion tracking
- ✅ Payment processing with Stripe integration
- ✅ Production-grade security and error handling
- ✅ Comprehensive deployment documentation

## 🛠️ Technology Stack

### Backend (evoque-api)
- **Runtime**: Node.js 18+ with TypeScript
- **API**: GraphQL with Apollo Server, REST endpoints
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with bcrypt
- **Payments**: Stripe integration
- **AI**: OpenAI API integration
- **Real-time**: WebSocket subscriptions

### Frontend (evoque-landing, evoque-dashboard)
- **Framework**: Next.js 13+ with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion, GSAP, Lottie
- **State Management**: Apollo Client (GraphQL)
- **Authentication**: NextAuth.js

### Widget (evoque-widget)
- **Framework**: React with TypeScript
- **Bundling**: Webpack with custom configurations
- **Deployment**: CDN-ready with universal loader
- **Compatibility**: All major website platforms

### Infrastructure (evoque-devops)
- **Containerization**: Docker with Docker Compose
- **Web Server**: NGINX with SSL
- **Process Management**: PM2
- **Deployment**: Automated scripts for VPS deployment

## 📚 Documentation

- **[FINAL_IMPLEMENTATION_SUMMARY.md](FINAL_IMPLEMENTATION_SUMMARY.md)** - Complete feature overview
- **[evoque-devops/DEPLOYMENT.md](evoque-devops/DEPLOYMENT.md)** - Production deployment guide
- **[evoque-widget/INSTALLATION.md](evoque-widget/INSTALLATION.md)** - Widget installation guide
- **[NEXT_STEPS.md](NEXT_STEPS.md)** - Roadmap and future development

## 📞 Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: See individual component README files
- Issues: Use GitHub issues for bug reports and feature requests

## 📄 License

This project is licensed under the MIT License - see individual component licenses for details.