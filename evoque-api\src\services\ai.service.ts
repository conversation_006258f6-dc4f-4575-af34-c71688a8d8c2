import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import axios from 'axios';
import { env } from '../config/env';

/**
 * AI Service
 * Handles all AI-related functionality including:
 * - OpenRouter API integration
 * - Vector embedding generation
 * - RAG system
 * - Context management
 * - Lead scoring
 * - Follow-up recommendations
 */
export class AIService {
  private static prisma = new PrismaClient();
  private static openaiApiKey = env.OPENAI_API_KEY; // For embeddings
  private static openrouterApiKey = env.OPENROUTER_API_KEY; // For chat
  private static openrouterModel = env.OPENROUTER_MODEL || 'deepseek/deepseek-chat-v3-0324:free';
  private static embeddingDimensions = 1536; // OpenAI embedding dimensions

  /**
   * Generate embeddings for a knowledge base item
   * @param knowledgeBaseItemId ID of the knowledge base item
   * @returns Boolean indicating success
   */
  public static async generateEmbeddings(knowledgeBaseItemId: string): Promise<boolean> {
    try {
      // Get the knowledge base item
      const kbItem = await this.prisma.knowledgeBaseItem.findUnique({
        where: { id: knowledgeBaseItemId },
      });

      if (!kbItem) {
        throw new Error(`Knowledge base item with ID ${knowledgeBaseItemId} not found`);
      }

      // Delete existing vectors for this item
      await this.prisma.kBVector.deleteMany({
        where: { kbId: knowledgeBaseItemId },
      });

      // Get or create embedding model
      const embeddingModel = await this.getOrCreateEmbeddingModel();

      // Split content into chunks
      const chunks = this.chunkText(kbItem.content);

      // Generate embeddings for each chunk
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const embedding = await this.generateEmbedding(chunk);

        // Store the embedding
        await this.prisma.kBVector.create({
          data: {
            kbId: knowledgeBaseItemId,
            chunkIndex: i,
            chunkText: chunk,
            embedding: embedding as any, // Cast to any due to Prisma vector type
            modelId: embeddingModel.id,
          },
        });
      }

      logger.info(`Generated embeddings for knowledge base item ${knowledgeBaseItemId}`);
      return true;
    } catch (error) {
      logger.error(`Error generating embeddings for knowledge base item ${knowledgeBaseItemId}:`, error);
      throw error;
    }
  }

  /**
   * Generate embeddings for all knowledge base items in a venue
   * @param venueId ID of the venue
   * @returns Boolean indicating success
   */
  public static async generateAllEmbeddings(venueId: string): Promise<boolean> {
    try {
      // Get all knowledge base items for the venue
      const kbItems = await this.prisma.knowledgeBaseItem.findMany({
        where: { venueId },
      });

      // Generate embeddings for each item
      for (const item of kbItems) {
        await this.generateEmbeddings(item.id);
      }

      logger.info(`Generated embeddings for all knowledge base items in venue ${venueId}`);
      return true;
    } catch (error) {
      logger.error(`Error generating embeddings for venue ${venueId}:`, error);
      throw error;
    }
  }

  /**
   * Get or create embedding model
   * @returns Embedding model
   */
  private static async getOrCreateEmbeddingModel(): Promise<any> {
    try {
      // Check if model exists
      let model = await this.prisma.embeddingModel.findFirst({
        where: {
          provider: 'openai',
          name: 'text-embedding-3-small',
          dimensions: this.embeddingDimensions,
        },
      });

      if (model) {
        return model;
      }

      // Create new model
      model = await this.prisma.embeddingModel.create({
        data: {
          name: 'text-embedding-3-small',
          provider: 'openai',
          version: '1',
          dimensions: this.embeddingDimensions,
        },
      });

      return model;
    } catch (error) {
      logger.error('Error getting or creating embedding model:', error);
      throw error;
    }
  }

  /**
   * Generate an AI response for an inquiry
   * @param inquiryId ID of the inquiry
   * @param message User message
   * @param includeKnowledgeBase Whether to include knowledge base in the response
   * @returns AI response
   */
  public static async generateResponse(inquiryId: string, message: string, includeKnowledgeBase: boolean = true): Promise<any> {
    try {
      // Get the inquiry
      const inquiry = await this.prisma.inquiry.findUnique({
        where: { id: inquiryId },
        include: {
          venue: true,
          messages: {
            orderBy: { sentAt: 'asc' },
          },
        },
      });

      if (!inquiry) {
        throw new Error(`Inquiry with ID ${inquiryId} not found`);
      }

      // Get or create context
      let context = await this.getOrCreateContext(inquiryId);

      // Add user message to context
      context = await this.addMessageToContext(context.id, 'user', message);

      // Retrieve relevant knowledge base items if requested
      let relevantContext = '';
      if (includeKnowledgeBase) {
        const relevantItems = await this.retrieveRelevantKnowledgeBase(inquiry.venueId, message);
        relevantContext = relevantItems.map((item: any) => `${item.chunkText}`).join('\n\n');
      }

      // Build prompt
      const prompt = this.buildPrompt(inquiry, context, relevantContext);

      // Generate response using OpenRouter API
      const response = await this.callOpenRouter(prompt);

      // Add assistant message to context
      await this.addMessageToContext(context.id, 'assistant', response.message);

      // Create inquiry message
      const inquiryMessage = await this.prisma.inquiryMessage.create({
        data: {
          inquiryId,
          direction: 'outbound',
          channel: 'ai',
          content: response.message,
          metadata: {},
        },
      });

      return {
        message: response.message,
        metadata: {
          contextId: context.id,
          inquiryMessageId: inquiryMessage.id,
        },
      };
    } catch (error) {
      logger.error(`Error generating AI response for inquiry ${inquiryId}:`, error);
      throw error;
    }
  }

  /**
   * Get or create context for an inquiry
   * @param inquiryId ID of the inquiry
   * @returns Context object
   */
  public static async getOrCreateContext(inquiryId: string): Promise<any> {
    try {
      // Check if context exists
      let context = await this.prisma.$queryRaw`
        SELECT id, inquiry_id as "inquiryId", messages, metadata, created_at as "createdAt", updated_at as "updatedAt"
        FROM ai_contexts
        WHERE inquiry_id = ${inquiryId}
      `;

      if (Array.isArray(context) && context.length > 0) {
        return context[0];
      }

      // Create new context
      context = await this.prisma.$executeRaw`
        INSERT INTO ai_contexts (id, inquiry_id, messages, metadata, created_at, updated_at)
        VALUES (gen_random_uuid(), ${inquiryId}, '[]', '{}', NOW(), NOW())
        RETURNING id, inquiry_id as "inquiryId", messages, metadata, created_at as "createdAt", updated_at as "updatedAt"
      `;

      // Get the newly created context
      const newContext = await this.prisma.$queryRaw`
        SELECT id, inquiry_id as "inquiryId", messages, metadata, created_at as "createdAt", updated_at as "updatedAt"
        FROM ai_contexts
        WHERE inquiry_id = ${inquiryId}
        ORDER BY created_at DESC
        LIMIT 1
      `;

      if (Array.isArray(newContext) && newContext.length > 0) {
        return newContext[0];
      }

      throw new Error(`Failed to create context for inquiry ${inquiryId}`);
    } catch (error) {
      logger.error(`Error getting or creating context for inquiry ${inquiryId}:`, error);
      throw error;
    }
  }

  /**
   * Get context for an inquiry
   * @param inquiryId ID of the inquiry
   * @returns Context object
   */
  public static async getContext(inquiryId: string): Promise<any> {
    try {
      const context = await this.prisma.$queryRaw`
        SELECT id, inquiry_id as "inquiryId", messages, metadata, created_at as "createdAt", updated_at as "updatedAt"
        FROM ai_contexts
        WHERE inquiry_id = ${inquiryId}
      `;

      if (Array.isArray(context) && context.length > 0) {
        return context[0];
      }

      return null;
    } catch (error) {
      logger.error(`Error getting context for inquiry ${inquiryId}:`, error);
      throw error;
    }
  }

  /**
   * Add message to context
   * @param contextId ID of the context
   * @param role Role of the message sender (user or assistant)
   * @param content Content of the message
   * @returns Updated context
   */
  private static async addMessageToContext(contextId: string, role: string, content: string): Promise<any> {
    try {
      // Get current context
      const context = await this.prisma.$queryRaw`
        SELECT id, messages
        FROM ai_contexts
        WHERE id = ${contextId}
      `;

      if (!Array.isArray(context) || context.length === 0) {
        throw new Error(`Context with ID ${contextId} not found`);
      }

      // Parse messages
      let messages = [];
      try {
        messages = JSON.parse(context[0].messages);
      } catch (e) {
        messages = [];
      }

      // Add new message
      messages.push({
        role,
        content,
        timestamp: new Date().toISOString(),
      });

      // Update context
      await this.prisma.$executeRaw`
        UPDATE ai_contexts
        SET messages = ${JSON.stringify(messages)}, updated_at = NOW()
        WHERE id = ${contextId}
      `;

      // Get updated context
      const updatedContext = await this.prisma.$queryRaw`
        SELECT id, inquiry_id as "inquiryId", messages, metadata, created_at as "createdAt", updated_at as "updatedAt"
        FROM ai_contexts
        WHERE id = ${contextId}
      `;

      if (Array.isArray(updatedContext) && updatedContext.length > 0) {
        return updatedContext[0];
      }

      throw new Error(`Failed to update context ${contextId}`);
    } catch (error) {
      logger.error(`Error adding message to context ${contextId}:`, error);
      throw error;
    }
  }

  /**
   * Retrieve relevant knowledge base items for a query
   * @param venueId ID of the venue
   * @param query Query text
   * @param limit Maximum number of results
   * @returns Array of relevant knowledge base items
   */
  private static async retrieveRelevantKnowledgeBase(venueId: string, query: string, limit: number = 5): Promise<any[]> {
    try {
      // Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(query);

      // Perform vector similarity search
      const results = await this.prisma.$queryRaw`
        SELECT 
          kv.id,
          kv.kb_id as "kbId",
          kv.chunk_text as "chunkText",
          kv.chunk_index as "chunkIndex",
          kb.title,
          1 - (kv.embedding <=> ${queryEmbedding}::vector) as "score"
        FROM 
          kb_vectors kv
        JOIN 
          knowledge_base kb ON kv.kb_id = kb.id
        WHERE 
          kb.venue_id = ${venueId}
          AND kb.status = 'active'
        ORDER BY 
          score DESC
        LIMIT ${limit}
      `;

      return Array.isArray(results) ? results : [];
    } catch (error) {
      logger.error(`Error retrieving relevant knowledge base for venue ${venueId}:`, error);
      return [];
    }
  }

  /**
   * Score a lead using AI
   * @param leadId ID of the lead
   * @param includeFactors Whether to include scoring factors
   * @returns Lead score
   */
  public static async scoreLeadWithAI(leadId: string, includeFactors: boolean = true): Promise<any> {
    try {
      // Get the lead with related data
      const lead = await this.prisma.lead.findUnique({
        where: { id: leadId },
        include: {
          venue: true,
          inquiry: {
            include: {
              messages: true,
            },
          },
          activities: true,
          tasks: true,
          noteEntries: true,
        },
      });

      if (!lead) {
        throw new Error(`Lead with ID ${leadId} not found`);
      }

      // Calculate base score
      let baseScore = 50; // Start with a neutral score

      // Factor 1: Event date proximity
      let dateScore = 0;
      if (lead.eventDate) {
        const today = new Date();
        const eventDate = new Date(lead.eventDate);
        const monthsUntilEvent = (eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24 * 30);
        
        // Higher score for events 3-9 months away (ideal booking window)
        if (monthsUntilEvent >= 3 && monthsUntilEvent <= 9) {
          dateScore = 20;
        } else if (monthsUntilEvent > 9 && monthsUntilEvent <= 18) {
          dateScore = 15;
        } else if (monthsUntilEvent > 0 && monthsUntilEvent < 3) {
          dateScore = 10; // Urgent but might be last-minute
        } else {
          dateScore = 5; // Either too far out or past
        }
      }

      // Factor 2: Guest count (higher guest count = higher revenue potential)
      let guestScore = 0;
      if (lead.guestCount) {
        if (lead.guestCount >= 200) {
          guestScore = 20;
        } else if (lead.guestCount >= 150) {
          guestScore = 15;
        } else if (lead.guestCount >= 100) {
          guestScore = 10;
        } else if (lead.guestCount >= 50) {
          guestScore = 5;
        }
      }

      // Factor 3: Engagement level
      let engagementScore = 0;
      const messageCount = lead.inquiry?.messages.length || 0;
      const noteCount = lead.noteEntries.length;
      const activityCount = lead.activities.length;
      
      const totalEngagements = messageCount + noteCount + activityCount;
      if (totalEngagements >= 10) {
        engagementScore = 20;
      } else if (totalEngagements >= 5) {
        engagementScore = 15;
      } else if (totalEngagements >= 3) {
        engagementScore = 10;
      } else if (totalEngagements >= 1) {
        engagementScore = 5;
      }

      // Factor 4: Budget range
      let budgetScore = 0;
      if (lead.budgetRange) {
        // This would need to be customized based on venue's pricing tiers
        if (lead.budgetRange.includes('high') || lead.budgetRange.includes('premium')) {
          budgetScore = 20;
        } else if (lead.budgetRange.includes('medium') || lead.budgetRange.includes('standard')) {
          budgetScore = 10;
        } else {
          budgetScore = 5;
        }
      }

      // Calculate final score
      const finalScore = Math.min(100, Math.max(0, baseScore + dateScore + guestScore + engagementScore + budgetScore));

      // Update lead score in database
      await this.prisma.lead.update({
        where: { id: leadId },
        data: { score: finalScore },
      });

      // Create or update lead score record
      const scoreRecord = await this.prisma.$executeRaw`
        INSERT INTO lead_scores (id, lead_id, score, factors, created_at, updated_at)
        VALUES (
          gen_random_uuid(),
          ${leadId},
          ${finalScore},
          ${JSON.stringify([
            { name: 'Event Date', score: dateScore, weight: 1.0, description: 'Based on proximity to ideal booking window' },
            { name: 'Guest Count', score: guestScore, weight: 1.0, description: 'Based on potential revenue from guest count' },
            { name: 'Engagement', score: engagementScore, weight: 1.0, description: 'Based on interaction frequency' },
            { name: 'Budget', score: budgetScore, weight: 1.0, description: 'Based on budget range' },
          ])},
          NOW(),
          NOW()
        )
        ON CONFLICT (lead_id)
        DO UPDATE SET
          score = ${finalScore},
          factors = ${JSON.stringify([
            { name: 'Event Date', score: dateScore, weight: 1.0, description: 'Based on proximity to ideal booking window' },
            { name: 'Guest Count', score: guestScore, weight: 1.0, description: 'Based on potential revenue from guest count' },
            { name: 'Engagement', score: engagementScore, weight: 1.0, description: 'Based on interaction frequency' },
            { name: 'Budget', score: budgetScore, weight: 1.0, description: 'Based on budget range' },
          ])},
          updated_at = NOW()
        RETURNING id, lead_id as "leadId", score, factors, created_at as "createdAt", updated_at as "updatedAt"
      `;

      // Get the lead score record
      const leadScore = await this.prisma.$queryRaw`
        SELECT id, lead_id as "leadId", score, factors, created_at as "createdAt", updated_at as "updatedAt"
        FROM lead_scores
        WHERE lead_id = ${leadId}
      `;

      if (Array.isArray(leadScore) && leadScore.length > 0) {
        return leadScore[0];
      }

      throw new Error(`Failed to create or update lead score for lead ${leadId}`);
    } catch (error) {
      logger.error(`Error scoring lead ${leadId}:`, error);
      throw error;
    }
/**
   * Get lead score
   * @param leadId ID of the lead
   * @returns Lead score
   */
  public static async getLeadScore(leadId: string): Promise<any> {
    try {
      const leadScore = await this.prisma.$queryRaw`
        SELECT id, lead_id as "leadId", score, factors, created_at as "createdAt", updated_at as "updatedAt"
        FROM lead_scores
        WHERE lead_id = ${leadId}
      `;

      if (Array.isArray(leadScore) && leadScore.length > 0) {
        return leadScore[0];
      }

      // If no score exists, generate one
      return this.scoreLeadWithAI(leadId);
    } catch (error) {
      logger.error(`Error getting lead score for lead ${leadId}:`, error);
      throw error;
    }
  }

  /**
   * Generate follow-up recommendation for a lead
   * @param leadId ID of the lead
   * @param type Type of follow-up
   * @returns Follow-up recommendation
   */
  public static async generateFollowUpRecommendation(leadId: string, type?: string): Promise<any> {
    try {
      // Get the lead with related data
      const lead = await this.prisma.lead.findUnique({
        where: { id: leadId },
        include: {
          venue: true,
          inquiry: {
            include: {
              messages: {
                orderBy: { sentAt: 'desc' },
                take: 5,
              },
            },
          },
          activities: {
            orderBy: { performedAt: 'desc' },
            take: 5,
          },
        },
      });

      if (!lead) {
        throw new Error(`Lead with ID ${leadId} not found`);
      }

      // Determine follow-up type if not provided
      if (!type) {
        // Check last contact date
        const lastContacted = lead.lastContacted;
        const now = new Date();
        
        if (!lastContacted) {
          type = 'initial';
        } else {
          const daysSinceContact = (now.getTime() - new Date(lastContacted).getTime()) / (1000 * 60 * 60 * 24);
          
          if (daysSinceContact > 14) {
            type = 'reconnect';
          } else if (daysSinceContact > 7) {
            type = 'check_in';
          } else {
            type = 'information';
          }
        }
      }

      // Generate follow-up message based on type
      let message = '';
      let suggestedDate = new Date();
      suggestedDate.setDate(suggestedDate.getDate() + 3); // Default to 3 days from now
      let priority = 2; // Medium priority by default

      switch (type) {
        case 'initial':
          message = `Hi ${lead.contactName},\n\nThank you for your interest in ${lead.venue.name} for your event${lead.eventDate ? ` on ${new Date(lead.eventDate).toLocaleDateString()}` : ''}. I'd love to discuss your vision and how we can make your special day unforgettable.\n\nWould you be available for a quick call this week to discuss your needs in more detail?\n\nLooking forward to hearing from you!`;
          priority = 1; // High priority
          break;
        
        case 'reconnect':
          message = `Hi ${lead.contactName},\n\nI hope you're doing well! I wanted to check in regarding your event${lead.eventDate ? ` planned for ${new Date(lead.eventDate).toLocaleDateString()}` : ''}. We haven't connected in a while, and I wanted to see if you have any questions or if there's anything I can help with.\n\nWould you still be interested in ${lead.venue.name} as your venue? I'd be happy to provide any additional information or schedule a tour if you haven't had a chance to visit yet.`;
          priority = 1; // High priority
          break;
        
        case 'check_in':
          message = `Hi ${lead.contactName},\n\nI hope you're having a great week! I'm just checking in to see if you've had a chance to consider ${lead.venue.name} for your event${lead.eventDate ? ` on ${new Date(lead.eventDate).toLocaleDateString()}` : ''}.\n\nDo you have any questions I can answer or would you like to schedule a time to discuss your needs further?`;
          priority = 2; // Medium priority
          break;
        
        case 'information':
          message = `Hi ${lead.contactName},\n\nI wanted to share some additional information about our ${lead.eventType || 'wedding'} packages at ${lead.venue.name} that might be helpful for your planning.\n\nWe offer several customizable options that can be tailored to your specific vision and budget. Would you like me to send over our detailed pricing guide and package information?\n\nI'm also available to discuss any specific questions you might have.`;
          priority = 3; // Lower priority
          break;
        
        default:
          message = `Hi ${lead.contactName},\n\nI hope you're doing well! I wanted to follow up regarding your event at ${lead.venue.name}${lead.eventDate ? ` planned for ${new Date(lead.eventDate).toLocaleDateString()}` : ''}.\n\nIs there anything specific you'd like to know more about? I'm here to help make your event planning as smooth as possible.`;
          priority = 2; // Medium priority
      }

      // Create follow-up recommendation
      const recommendation = await this.prisma.$executeRaw`
        INSERT INTO follow_up_recommendations (
          id, 
          lead_id, 
          recommendation_type, 
          message, 
          suggested_date, 
          priority, 
          metadata, 
          created_at
        )
        VALUES (
          gen_random_uuid(),
          ${leadId},
          ${type},
          ${message},
          ${suggestedDate.toISOString()},
          ${priority},
          '{}',
          NOW()
        )
        RETURNING 
          id, 
          lead_id as "leadId", 
          recommendation_type as "recommendationType", 
          message, 
          suggested_date as "suggestedDate", 
          priority, 
          metadata, 
          created_at as "createdAt"
      `;

      // Get the newly created recommendation
      const newRecommendation = await this.prisma.$queryRaw`
        SELECT 
          id, 
          lead_id as "leadId", 
          recommendation_type as "recommendationType", 
          message, 
          suggested_date as "suggestedDate", 
          priority, 
          metadata, 
          created_at as "createdAt"
        FROM follow_up_recommendations
        WHERE lead_id = ${leadId}
        ORDER BY created_at DESC
        LIMIT 1
      `;

      if (Array.isArray(newRecommendation) && newRecommendation.length > 0) {
        return newRecommendation[0];
      }

      throw new Error(`Failed to create follow-up recommendation for lead ${leadId}`);
    } catch (error) {
      logger.error(`Error generating follow-up recommendation for lead ${leadId}:`, error);
      throw error;
    }
  }

  /**
   * Get follow-up recommendations for a lead
   * @param leadId ID of the lead
   * @returns Array of follow-up recommendations
   */
  public static async getFollowUpRecommendations(leadId: string): Promise<any[]> {
    try {
      const recommendations = await this.prisma.$queryRaw`
        SELECT 
          id, 
          lead_id as "leadId", 
          recommendation_type as "recommendationType", 
          message, 
          suggested_date as "suggestedDate", 
          priority, 
          metadata, 
          created_at as "createdAt"
        FROM follow_up_recommendations
        WHERE lead_id = ${leadId}
        ORDER BY created_at DESC
      `;

      return Array.isArray(recommendations) ? recommendations : [];
    } catch (error) {
      logger.error(`Error getting follow-up recommendations for lead ${leadId}:`, error);
      return [];
    }
  }

  /**
   * Generate embedding for text
   * @param text Text to embed
   * @returns Embedding vector
   */
  private static async generateEmbedding(text: string): Promise<number[]> {
    try {
      const response = await axios.post(
        'https://api.openai.com/v1/embeddings',
        {
          input: text,
          model: 'text-embedding-3-small',
        },
        {
          headers: {
            'Authorization': `Bearer ${this.openaiApiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data.data[0].embedding;
    } catch (error) {
      logger.error('Error generating embedding:', error);
      throw error;
    }
  }

  /**
   * Call OpenRouter API to generate a response
   * @param prompt Prompt to send to the API
   * @returns Generated response
   */
  private static async callOpenRouter(prompt: any): Promise<any> {
    try {
      const response = await axios.post(
        'https://openrouter.ai/api/v1/chat/completions',
        {
          model: this.openrouterModel,
          messages: prompt,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.openrouterApiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://evoque.digital',
            'X-Title': 'Evoque.Digital Wedding Venue Management',
          },
        }
      );

      return {
        message: response.data.choices[0].message.content,
        usage: response.data.usage,
      };
    } catch (error) {
      logger.error('Error calling OpenRouter API:', error);
      throw error;
    }
  }

  /**
   * Build prompt for the AI model
   * @param inquiry Inquiry object
   * @param context Context object
   * @param relevantContext Relevant knowledge base content
   * @returns Formatted prompt
   */
  private static buildPrompt(inquiry: any, context: any, relevantContext: string): any[] {
    // System message with instructions
    const systemMessage = {
      role: 'system',
      content: `You are an AI assistant for ${inquiry.venue.name}, a wedding venue. Your name is ENGAGE.
Your goal is to provide helpful, accurate, and friendly responses to inquiries about the venue.
Be conversational but professional. Focus on being helpful and providing relevant information.
Always be truthful and if you don't know something, say so rather than making up information.

${relevantContext ? 'Here is some information about the venue that may be helpful:\n\n' + relevantContext : ''}

When responding to inquiries:
1. Be warm and welcoming
2. Provide specific information when available
3. Ask clarifying questions when needed
4. Suggest next steps (tour, call, etc.) when appropriate
5. Never make up information about pricing, availability, or venue details not provided to you`
    };

    // Parse context messages
    let contextMessages = [];
    try {
      contextMessages = JSON.parse(context.messages);
    } catch (e) {
      contextMessages = [];
    }

    // Format messages for the API
    const messages = [
      systemMessage,
      ...contextMessages.map((msg: any) => ({
        role: msg.role,
        content: msg.content,
      })),
    ];

    return messages;
  }

  /**
   * Split text into chunks for embedding
   * @param text Text to split
   * @param maxChunkLength Maximum length of each chunk
   * @returns Array of text chunks
   */
  private static chunkText(text: string, maxChunkLength: number = 1000): string[] {
    const chunks: string[] = [];
    
    // Split by paragraphs first
    const paragraphs = text.split(/\n\s*\n/);
    
    let currentChunk = '';
    
    for (const paragraph of paragraphs) {
      // If adding this paragraph would exceed max length, save current chunk and start a new one
      if (currentChunk.length + paragraph.length > maxChunkLength && currentChunk.length > 0) {
        chunks.push(currentChunk.trim());
        currentChunk = '';
      }
      
      // If paragraph itself is too long, split it into sentences
      if (paragraph.length > maxChunkLength) {
        const sentences = paragraph.match(/[^.!?]+[.!?]+/g) || [paragraph];
        
        for (const sentence of sentences) {
          if (currentChunk.length + sentence.length > maxChunkLength && currentChunk.length > 0) {
            chunks.push(currentChunk.trim());
            currentChunk = '';
          }
          
          currentChunk += sentence + ' ';
        }
      } else {
        currentChunk += paragraph + '\n\n';
      }
    }
    
    // Add the last chunk if it's not empty
    if (currentChunk.trim().length > 0) {
      chunks.push(currentChunk.trim());
    }
    
    return chunks;
  }
}
  }