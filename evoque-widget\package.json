{"name": "evoque-widget", "version": "1.0.0", "description": "Embeddable chat widget for Evoque.Digital platform", "main": "build/index.js", "scripts": {"start": "webpack serve --mode development", "build": "npm run build:widget && npm run build:loader", "build:widget": "webpack --mode production", "build:loader": "webpack --config webpack.loader.config.js --mode production", "build:dev": "npm run build:widget:dev && npm run build:loader:dev", "build:widget:dev": "webpack --mode development", "build:loader:dev": "webpack --config webpack.loader.config.js --mode development", "dev": "concurrently \"npm run start\" \"ts-node src/dev-server.ts\"", "test": "jest", "lint": "eslint src/**/*.{ts,tsx}"}, "keywords": ["evoque", "chat", "widget", "embeddable"], "author": "Evoque.Digital", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "socket.io-client": "^4.7.2", "styled-components": "^6.1.1", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/preset-env": "^7.23.3", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/styled-components": "^5.1.30", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "babel-loader": "^9.1.3", "concurrently": "^8.2.2", "css-loader": "^6.8.1", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.7.0", "mock-socket": "^9.2.1", "style-loader": "^3.3.3", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "typescript": "^5.2.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}}